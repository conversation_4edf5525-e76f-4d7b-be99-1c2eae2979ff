// API base URL
const API_BASE_URL = '/api';

// App state
window.state = {
  isAuthenticated: false,
  admin: null,
  token: null,
  currentPage: 'login',
};

// DOM elements
const app = document.getElementById('app');
const navItems = document.getElementById('nav-items');
const authItems = document.getElementById('auth-items');

// Initialize the app
async function init() {
  console.log('Initializing app...');

  // Wait for translations to be loaded
  if (!translationsLoaded) {
    console.log('Waiting for translations to load...');
    await new Promise(resolve => {
      const checkTranslations = () => {
        if (translationsLoaded) {
          console.log('Translations loaded, continuing app initialization');
          resolve();
        } else {
          setTimeout(checkTranslations, 100);
        }
      };
      checkTranslations();
    });
  }

  // Check if token exists in localStorage
  const token = localStorage.getItem('token');
  if (token) {
    window.state.token = token;
    window.state.isAuthenticated = true;
    window.state.admin = JSON.parse(localStorage.getItem('admin'));

    // Set axios default header
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Verify token is still valid and admin is active
    axios.get(`${API_BASE_URL}/auth/profile`)
      .then(() => {
        // Load dashboard
        loadDashboard();
      })
      .catch(() => {
        // If error, logout and redirect to login
        logout();
      });
  } else {
    // Load login page
    loadLoginPage();
  }

  // Update navigation
  updateNavigation();

  console.log('App initialization complete');
}

// Update navigation based on authentication state
function updateNavigation() {
  // Eğer bildirimler sayfasından başka bir sayfaya geçiliyorsa zamanlayıcıyı durdur
  if (window.state.currentPage === 'notifications') {
    stopNotificationRefresh();
  }

  // Clear navigation
  navItems.innerHTML = '';
  authItems.innerHTML = '';

  if (window.state.isAuthenticated) {
    // Add navigation items for authenticated users
    navItems.innerHTML = `
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'dashboard' ? 'active' : ''}" href="#" onclick="loadDashboard()" data-i18n="app.nav.dashboard">Ana Sayfa</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'contests' ? 'active' : ''}" href="#" onclick="loadContests()" data-i18n="app.nav.contests">Yarışmalar</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'users' ? 'active' : ''}" href="#" onclick="loadUsers()" data-i18n="app.nav.users">Kullanıcılar</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'settings' ? 'active' : ''}" href="#" onclick="loadSettings()" data-i18n="app.nav.settings">Bot Ayarları</a>
      </li>
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'notifications' ? 'active' : ''}" href="#" onclick="loadNotifications()" data-i18n="app.nav.notifications">Bildirimler</a>
      </li>
      ${window.state.admin && window.state.admin.role === 'superadmin' ? `
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'admins' || window.state.currentPage === 'create-admin' ? 'active' : ''}" href="#" onclick="loadAdmins()" data-i18n="app.nav.admins">Yöneticiler</a>
      </li>
      ` : ''}
    `;

    // Add auth items
    authItems.innerHTML = `
      <li class="nav-item">
        <span class="nav-link welcome-message">${i18next.t('app.common.welcome')}, ${window.state.admin.name}</span>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#" onclick="logout()" data-i18n="app.nav.logout">Çıkış Yap</a>
      </li>
    `;
  } else {
    // Add auth items for non-authenticated users
    authItems.innerHTML = `
      <li class="nav-item">
        <a class="nav-link ${window.state.currentPage === 'login' ? 'active' : ''}" href="#" onclick="loadLoginPage()" data-i18n="app.login.title">Giriş Yap</a>
      </li>
    `;
  }

  // Update translations
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    element.textContent = i18next.t(key);
  });
}

// Load login page
function loadLoginPage() {
  window.state.currentPage = 'login';
  updateNavigation();

  app.innerHTML = `
    <div class="login-container">
      <h2 class="text-center mb-4" data-i18n="app.login.title">Yönetici Girişi</h2>
      <form id="login-form">
        <div class="mb-3">
          <label for="username" class="form-label" data-i18n="app.login.username">Kullanıcı Adı</label>
          <input type="text" class="form-control" id="username" required>
        </div>
        <div class="mb-3">
          <label for="password" class="form-label" data-i18n="app.login.password">Şifre</label>
          <input type="password" class="form-control" id="password" required>
        </div>
        <div id="login-error" class="alert alert-danger d-none"></div>
        <button type="submit" class="btn btn-primary w-100" data-i18n="app.login.button">Giriş Yap</button>
      </form>
    </div>
  `;

  // Add event listener to login form
  document.getElementById('login-form').addEventListener('submit', handleLogin);
}

// Handle login form submission
async function handleLogin(e) {
  e.preventDefault();

  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const errorElement = document.getElementById('login-error');

  try {
    errorElement.classList.add('d-none');

    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username,
      password,
    });

    // Save token and admin info
    const { token, admin } = response.data;
    localStorage.setItem('token', token);
    localStorage.setItem('admin', JSON.stringify(admin));

    // Update state
    window.state.token = token;
    window.state.admin = admin;
    window.state.isAuthenticated = true;

    // Set axios default header
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Load dashboard
    loadDashboard();

    // Update navigation
    updateNavigation();
  } catch (error) {
    console.error('Login error:', error);
    // Check if it's an inactive account error
    if (error.response?.status === 403 && error.response?.data?.message?.includes('deactivated')) {
      errorElement.textContent = i18next.t('app.login.inactive');
    } else {
      errorElement.textContent = error.response?.data?.message || i18next.t('app.login.error');
    }
    errorElement.classList.remove('d-none');
  }
}

// Logout function
function logout() {
  // Clear localStorage
  localStorage.removeItem('token');
  localStorage.removeItem('admin');

  // Reset state
  window.state.token = null;
  window.state.admin = null;
  window.state.isAuthenticated = false;

  // Remove axios default header
  delete axios.defaults.headers.common['Authorization'];

  // Load login page
  loadLoginPage();

  // Update navigation
  updateNavigation();
}

// Load dashboard
async function loadDashboard() {
  if (!window.state.isAuthenticated) {
    return loadLoginPage();
  }

  window.state.currentPage = 'dashboard';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get user stats
    const statsResponse = await axios.get(`${API_BASE_URL}/users/stats`);
    const { stats } = statsResponse.data;

    // Get active contests
    const contestsResponse = await axios.get(`${API_BASE_URL}/contests`);
    const { contests } = contestsResponse.data;

    // Filter active contests
    const activeContests = contests.filter(contest => contest.status === 'ACTIVE');

    // Render dashboard
    app.innerHTML = `
      <h1 class="mb-4" data-i18n="app.dashboard.title">Kontrol Paneli</h1>

      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.dashboard.totalUsers">Toplam Kullanıcı</h5>
              <p class="card-text display-4">${stats.totalUsers}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.dashboard.newUsers">Yeni Kullanıcılar (7g)</h5>
              <p class="card-text display-4">${stats.newUsers}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.dashboard.activeUsers">Aktif Kullanıcılar (7g)</h5>
              <p class="card-text display-4">${stats.activeUsers}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.dashboard.totalSubmissions">Toplam Katılım</h5>
              <p class="card-text display-4">${stats.totalSubmissions}</p>
            </div>
          </div>
        </div>
      </div>

      <h2 class="mb-3" data-i18n="app.dashboard.activeContests">Aktif Yarışmalar</h2>

      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th data-i18n="app.contestForm.title">Başlık</th>
              <th data-i18n="app.contests.endDate">Bitiş Tarihi</th>
              <th data-i18n="app.contestDetail.questions">Sorular</th>
              <th data-i18n="app.contests.actions">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            ${activeContests.length > 0 ? activeContests.map(contest => `
              <tr>
                <td>${contest.title}</td>
                <td>${new Date(contest.endDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                <td>${contest.questions.length}</td>
                <td>
                  <a href="#" onclick="viewContest('${contest._id}')" class="btn btn-sm btn-primary" data-i18n="app.contests.view">Görüntüle</a>
                </td>
              </tr>
            `).join('') : `
              <tr>
                <td colspan="4" class="text-center" data-i18n="app.dashboard.noContests">Aktif yarışma bulunmuyor</td>
              </tr>
            `}
          </tbody>
        </table>
      </div>

      <div class="text-center mt-4">
        <button class="btn btn-success" onclick="loadCreateContest()" data-i18n="app.contests.create">Yeni Yarışma Oluştur</button>
      </div>
    `;

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Dashboard error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.common.error')}
      </div>
    `;
  }
}

// Load contests page
async function loadContests() {
  if (!window.state.isAuthenticated) {
    return loadLoginPage();
  }

  window.state.currentPage = 'contests';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get contests
    const response = await axios.get(`${API_BASE_URL}/contests`);
    const { contests } = response.data;

    // Group contests by status
    const activeContests = contests.filter(contest => contest.status === 'ACTIVE');
    const draftContests = contests.filter(contest => contest.status === 'DRAFT');
    const completedContests = contests.filter(contest => contest.status === 'COMPLETED');
    const cancelledContests = contests.filter(contest => contest.status === 'CANCELLED');

    // Render contests page
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.contests.title">Yarışmalar</h1>
        <button class="btn btn-success" onclick="loadCreateContest()" data-i18n="app.contests.create">Yeni Yarışma Oluştur</button>
      </div>

      <!-- Contest Type Filter -->
      <div class="d-flex mb-3">
        <div class="me-3">
          <label for="contestTypeFilter" class="form-label" data-i18n="app.contests.filterByType">Yarışma Tipine Göre Filtrele</label>
          <select class="form-select" id="contestTypeFilter" onchange="filterContestsByType()">
            <option value="" data-i18n="app.contests.allTypes">Tüm Tipler</option>
            <option value="SPORTS_PREDICTION" data-i18n="app.contestForm.sportsPrediction">🏆 Spor Tahmin Yarışması</option>
            <option value="EMOJI_GAME" data-i18n="app.contestForm.emojiGame">🎮 Emoji Yarışması</option>
          </select>
        </div>
      </div>

      <ul class="nav nav-tabs mb-4" id="contestTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
            <span data-i18n="app.contests.active">Aktif</span> (<span id="activeCount">${activeContests.length}</span>)
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="draft-tab" data-bs-toggle="tab" data-bs-target="#draft" type="button" role="tab">
            <span data-i18n="app.common.draft">Taslak</span> (<span id="draftCount">${draftContests.length}</span>)
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab">
            <span data-i18n="app.common.completed">Tamamlanmış</span> (<span id="completedCount">${completedContests.length}</span>)
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab">
            <span data-i18n="app.contests.cancelled">İptal Edilmiş</span> (<span id="cancelledCount">${cancelledContests.length}</span>)
          </button>
        </li>
      </ul>

      <div class="tab-content" id="contestTabsContent">
        <div class="tab-pane fade show active" id="active" role="tabpanel">
          ${renderContestTable(activeContests)}
        </div>
        <div class="tab-pane fade" id="draft" role="tabpanel">
          ${renderContestTable(draftContests)}
        </div>
        <div class="tab-pane fade" id="completed" role="tabpanel">
          ${renderContestTable(completedContests)}
        </div>
        <div class="tab-pane fade" id="cancelled" role="tabpanel">
          ${renderContestTable(cancelledContests)}
        </div>
      </div>
    `;

    // Store original contests for filtering
    window.originalContests = contests;

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Contests error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.common.error')}
      </div>
    `;
  }
}

// Filter contests by type
function filterContestsByType() {
  const selectedType = document.getElementById('contestTypeFilter').value;
  const originalContests = window.originalContests || [];

  let filteredContests = originalContests;
  if (selectedType) {
    filteredContests = originalContests.filter(contest => contest.type === selectedType);
  }

  // Group filtered contests by status
  const activeContests = filteredContests.filter(contest => contest.status === 'ACTIVE');
  const draftContests = filteredContests.filter(contest => contest.status === 'DRAFT');
  const completedContests = filteredContests.filter(contest => contest.status === 'COMPLETED');
  const cancelledContests = filteredContests.filter(contest => contest.status === 'CANCELLED');

  // Update tab content
  document.getElementById('active').innerHTML = renderContestTable(activeContests);
  document.getElementById('draft').innerHTML = renderContestTable(draftContests);
  document.getElementById('completed').innerHTML = renderContestTable(completedContests);
  document.getElementById('cancelled').innerHTML = renderContestTable(cancelledContests);

  // Update tab counts
  document.getElementById('activeCount').textContent = activeContests.length;
  document.getElementById('draftCount').textContent = draftContests.length;
  document.getElementById('completedCount').textContent = completedContests.length;
  document.getElementById('cancelledCount').textContent = cancelledContests.length;

  // Update translations
  updateUI();
}

// Render contest table
function renderContestTable(contests) {
  if (contests.length === 0) {
    return `
      <div class="alert alert-info">
        ${i18next.t('app.contests.noContests')}
      </div>
    `;
  }

  // Determine if we need to show winner count column
  const showWinnerCount = contests.some(contest => contest.status === 'COMPLETED');

  return `
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th data-i18n="app.contestForm.title">Başlık</th>
            <th data-i18n="app.contestForm.contestType">Tip</th>
            <th data-i18n="app.contests.startDate">Başlangıç Tarihi</th>
            <th data-i18n="app.contests.endDate">Bitiş Tarihi</th>
            <th data-i18n="app.contestDetail.questionsOrGame">Sorular/Oyun</th>
            <th data-i18n="app.contests.participants">Katılımcılar</th>
            ${showWinnerCount ? `<th data-i18n="app.contests.winners">Kazananlar</th>` : ''}
            <th data-i18n="app.contests.creator">Oluşturan</th>
            <th data-i18n="app.contests.actions">İşlemler</th>
          </tr>
        </thead>
        <tbody>
          ${contests.map(contest => {
    // Get contest type display
    let contestTypeDisplay = '';
    if (contest.type === 'EMOJI_GAME') {
      const gameTypeEmojis = {
        'DICE': '🎲',
        'BASKETBALL': '🏀',
        'FOOTBALL': '⚽',
        'DART': '🎯',
        'BOWLING': '🎳',
        'SLOT': '🎰'
      };
      const emoji = gameTypeEmojis[contest.emojiGame?.gameType] || '🎮';
      contestTypeDisplay = `${emoji} Emoji Yarışması`;
    } else {
      contestTypeDisplay = '🏆 Spor Tahmin';
    }

    // Get questions/game info
    let questionsInfo = '';
    if (contest.type === 'EMOJI_GAME') {
      const gameTypeNames = {
        'DICE': '🎲 Zar',
        'BASKETBALL': '🏀 Basketbol',
        'FOOTBALL': '⚽ Futbol',
        'DART': '🎯 Dart',
        'BOWLING': '🎳 Bowling',
        'SLOT': '🎰 Slot'
      };
      const gameTypeName = gameTypeNames[contest.emojiGame?.gameType] || '🎮 Oyun';
      questionsInfo = `${gameTypeName} (${contest.emojiGame?.attemptCount || 0} deneme)`;
    } else {
      questionsInfo = `${contest.questions?.length || 0} soru`;
    }

    return `
            <tr>
              <td>${contest.title}</td>
              <td>${contestTypeDisplay}</td>
              <td>${new Date(contest.startDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
              <td>${new Date(contest.endDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
              <td>${questionsInfo}</td>
              <td>${contest.participantCount || 0}</td>
              ${showWinnerCount ? `<td>${contest.status === 'COMPLETED' ? contest.winnerCount || 0 : '-'}</td>` : ''}
              <td>${contest.createdBy.name}</td>
              <td>
                <div class="btn-group">
                  <a href="#" onclick="viewContest('${contest._id}')" class="btn btn-sm btn-primary" data-i18n="app.contests.view">Görüntüle</a>
                  ${contest.status !== 'COMPLETED' ? `
                    <a href="#" onclick="editContest('${contest._id}')" class="btn btn-sm btn-warning" data-i18n="app.contests.edit">Düzenle</a>
                    ${contest.status === 'DRAFT' ? `
                      <a href="#" onclick="activateContest('${contest._id}')" class="btn btn-sm btn-success" data-i18n="app.contests.activate">Aktifleştir</a>
                    ` : ''}
                    ${contest.status === 'ACTIVE' ? `
                      <a href="#" onclick="viewSubmissions('${contest._id}')" class="btn btn-sm btn-info" data-i18n="app.contestDetail.submissions">Katılımlar</a>
                      <button onclick="showCancelContestModal('${contest._id}')" class="btn btn-sm btn-danger" data-i18n="app.contests.cancel">İptal Et</button>
                    ` : ''}
                  ` : ''}
                  ${contest.status === 'COMPLETED' ? `
                    <a href="#" onclick="viewSubmissions('${contest._id}')" class="btn btn-sm btn-info" data-i18n="app.contestDetail.submissions">Katılımlar</a>
                    <button onclick="exportToExcel('${contest._id}')" class="btn btn-sm btn-success" data-i18n="app.contests.export">Dışa Aktar</button>
                    <button onclick="sendNotificationToWinners('${contest._id}')" class="btn btn-sm btn-primary" data-i18n="app.contestDetail.notifyWinners">Kazananlara Bildirim Gönder</button>
                  ` : ''}
                </div>
              </td>
            </tr>
            `;
  }).join('')}
        </tbody>
      </table>
    </div>
  `;
}

// Load users page
async function loadUsers(page = 1) {
  if (!window.state.isAuthenticated) {
    return loadLoginPage();
  }

  window.state.currentPage = 'users';
  updateNavigation();

  // Show loading
  app.innerHTML = `
  < div class="text-center" >
    <div class="spinner-border" role="status">
      <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
    </div>
    </div >
  `;

  try {
    // Render users page structure first
    app.innerHTML = `
  < h1 class="mb-4" data - i18n="app.users.title" > Kullanıcılar</h1 >

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0" data-i18n="app.users.filters">Filtreler</h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <label for="searchFilter" class="form-label" data-i18n="app.users.search">Ara</label>
              <input type="text" class="form-control" id="searchFilter" placeholder="Kullanıcı adı, Telegram ID, İsim...">
            </div>
            <div class="col-md-4">
              <label for="stateFilter" class="form-label" data-i18n="app.users.state">Durum</label>
              <select class="form-select" id="stateFilter">
                <option value="" data-i18n="app.common.all">Tümü</option>
                <option value="IDLE" data-i18n="app.users.stateIdle">Boşta</option>
                <option value="REGISTERING" data-i18n="app.users.stateRegistering">Kayıt Oluyor</option>
                <option value="ANSWERING" data-i18n="app.users.stateAnswering">Cevaplıyor</option>
                <option value="CONFIRMING" data-i18n="app.users.stateConfirming">Onaylıyor</option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="languageFilter" class="form-label" data-i18n="app.users.language">Dil</label>
              <select class="form-select" id="languageFilter">
                <option value="" data-i18n="app.common.all">Tümü</option>
                <option value="tr" data-i18n="app.languages.tr">Türkçe</option>
                <option value="en" data-i18n="app.languages.en">İngilizce</option>
                <option value="de" data-i18n="app.languages.de">Almanca</option>
                <option value="ar" data-i18n="app.languages.ar">Arapça</option>
              </select>
            </div>
          </div>
          <div class="mt-3">
            <button class="btn btn-primary" onclick="filterUsers()" data-i18n="app.users.filter">Filtrele</button>
            <button class="btn btn-secondary" onclick="resetUserFilters()" data-i18n="app.users.resetFilters">Filtreleri Sıfırla</button>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th data-i18n="app.users.username">Kullanıcı Adı</th>
              <th data-i18n="app.users.telegramId">Telegram ID</th>
              <th data-i18n="app.users.name">İsim</th>
              <th data-i18n="app.users.registrationDate">Kayıt Tarihi</th>
              <th data-i18n="app.users.state">Durum</th>
              <th data-i18n="app.users.language">Dil</th>
              <th data-i18n="app.userDetail.blockStatus">Blok Durumu</th>
              <th data-i18n="app.users.actions">İşlemler</th>
            </tr>
          </thead>
          <tbody id="usersTableBody">
            <tr>
              <td colspan="8" class="text-center">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <nav aria-label="Users pagination">
        <ul class="pagination justify-content-center" id="usersPagination"></ul>
      </nav>
`;

    // Update translations
    updateUI();

    // Load users with filters
    await filterUsers(page);

    // Initialize tooltips
    initTooltips();

  } catch (error) {
    console.error('Users error:', error);
    app.innerHTML = `
  < div class="alert alert-danger" >
    ${i18next.t('app.common.error')}
      </div >
  `;
  }
}

// Filter users
async function filterUsers(page = 1) {
  const searchFilter = document.getElementById('searchFilter')?.value || '';
  const stateFilter = document.getElementById('stateFilter')?.value || '';
  const languageFilter = document.getElementById('languageFilter')?.value || '';

  try {
    // Show loading
    document.getElementById('usersTableBody').innerHTML = `
  < tr >
  <td colspan="8" class="text-center">
    <div class="spinner-border spinner-border-sm" role="status">
      <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
    </div>
  </td>
      </tr >
  `;

    // Get users with filters
    const response = await axios.get(`${API_BASE_URL}/users`, {
      params: {
        search: searchFilter,
        state: stateFilter,
        language: languageFilter,
        page,
        limit: 10
      }
    });

    const { users, pagination } = response.data;

    // Update table
    document.getElementById('usersTableBody').innerHTML = users.length > 0 ? users.map(user => `
      <tr>
        <td>${user.username}</td>
        <td>${user.telegramId}</td>
        <td>${user.firstName || ''} ${user.lastName || ''}</td>
        <td>${new Date(user.registrationDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric' })}</td>
        <td>${user.state}</td>
        <td>${user.language}</td>
        <td>
          ${user.isBlocked ?
        `<span class="badge bg-danger" data-i18n="app.userDetail.blocked"
          data-bs-toggle="tooltip" data-bs-placement="top"
          title="${user.blockedBy ? `${i18next.t('app.userDetail.blockedBy')}: ${user.blockedBy.name}` : ''} ${user.blockedAt ? `\n${i18next.t('app.userDetail.blockedAt')}: ${new Date(user.blockedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}` : ''}">
          Bloklu
        </span>` :
        `<span class="badge bg-success" data-i18n="app.userDetail.notBlocked">Aktif</span>`
      }
        </td>
        <td>
          <a href="#" onclick="viewUser('${user._id}')" class="btn btn-sm btn-primary" data-i18n="app.users.view">Görüntüle</a>
          ${user.isBlocked ?
        `<button class="btn btn-sm btn-success" onclick="unblockUser('${user._id}')" data-i18n="app.userDetail.unblock">Blokajı Kaldır</button>` :
        `<button class="btn btn-sm btn-danger" onclick="showBlockUserModal('${user._id}')" data-i18n="app.userDetail.block">Blokla</button>`
      }
        </td>
      </tr>
    `).join('') : `
      <tr>
        <td colspan="8" class="text-center" data-i18n="app.users.noUsers">Kullanıcı bulunamadı</td>
      </tr>
    `;

    // Update pagination
    renderPagination(pagination, 'usersPagination', 'filterUsers');

    // Update translations
    updateUI();

    // Initialize tooltips
    initTooltips();
  } catch (error) {
    console.error('Filter users error:', error);
    document.getElementById('usersTableBody').innerHTML = `
      <tr>
        <td colspan="8" class="text-center text-danger">
          ${i18next.t('app.common.error')}
        </td>
      </tr>
    `;
  }
}

// Reset user filters
function resetUserFilters() {
  document.getElementById('searchFilter').value = '';
  document.getElementById('stateFilter').value = '';
  document.getElementById('languageFilter').value = '';
  filterUsers(1);
}

// Show block user modal
function showBlockUserModal(userId) {
  // Create modal if it doesn't exist
  if (!document.getElementById('blockUserModal')) {
    const modalHTML = `
      <div class="modal fade" id="blockUserModal" tabindex="-1" aria-labelledby="blockUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="blockUserModalLabel" data-i18n="app.userDetail.blockModalTitle">Kullanıcıyı Blokla</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <form id="blockUserForm">
                <input type="hidden" id="blockUserId" value="">
                <div class="mb-3">
                  <label for="blockReason" class="form-label" data-i18n="app.userDetail.blockReason">Blok Nedeni</label>
                  <textarea class="form-control" id="blockReason" rows="3"></textarea>
                  <div class="form-text" data-i18n="app.userDetail.blockReasonHelp">Kullanıcının neden bloklandığını açıklayın (isteğe bağlı)</div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-i18n="app.common.cancel">İptal</button>
              <button type="button" class="btn btn-danger" onclick="blockUser()" data-i18n="app.userDetail.confirmBlock">Blokla</button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    updateUI();
  }

  // Set user ID in the form
  document.getElementById('blockUserId').value = userId;
  document.getElementById('blockReason').value = '';

  // Show modal
  const blockModal = new bootstrap.Modal(document.getElementById('blockUserModal'));
  blockModal.show();
}

// Block user
async function blockUser() {
  try {
    const userId = document.getElementById('blockUserId').value;
    const reason = document.getElementById('blockReason').value;

    // Close modal
    const blockModal = bootstrap.Modal.getInstance(document.getElementById('blockUserModal'));
    blockModal.hide();

    // Show loading
    showToast(i18next.t('app.common.loading'), 'info');

    // Call API to block user
    const response = await axios.put(`${API_BASE_URL}/users/${userId}/block`, { reason });

    if (response.data.success) {
      // Show success message
      showToast(i18next.t('app.userDetail.blockSuccess'), 'success');

      // Reload current page
      if (window.state.currentPage === 'users') {
        filterUsers(1);
      } else {
        viewUser(userId);
      }
    }
  } catch (error) {
    console.error('Block user error:', error);
    showToast(i18next.t('app.userDetail.blockError'), 'danger');
  }
}

// Unblock user
async function unblockUser(userId) {
  try {
    // Show confirmation dialog
    if (!confirm(i18next.t('app.userDetail.unblockConfirm'))) {
      return;
    }

    // Show loading
    showToast(i18next.t('app.common.loading'), 'info');

    // Call API to unblock user
    const response = await axios.put(`${API_BASE_URL}/users/${userId}/unblock`);

    if (response.data.success) {
      // Show success message
      showToast(i18next.t('app.userDetail.unblockSuccess'), 'success');

      // Reload current page
      if (window.state.currentPage === 'users') {
        filterUsers(1);
      } else {
        viewUser(userId);
      }
    }
  } catch (error) {
    console.error('Unblock user error:', error);
    showToast(i18next.t('app.userDetail.unblockError'), 'danger');
  }
}

// Initialize tooltips
function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl, {
      html: true
    });
  });
}

// Show toast message
function showToast(message, type = 'info') {
  // Create toast container if it doesn't exist
  let toastContainer = document.querySelector('.toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(toastContainer);
  }

  // Create toast
  const toastId = 'toast-' + Date.now();
  const toastHTML = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="d-flex">
        <div class="toast-body">
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    </div>
  `;

  toastContainer.insertAdjacentHTML('beforeend', toastHTML);

  // Show toast
  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
  toast.show();

  // Remove toast after it's hidden
  toastElement.addEventListener('hidden.bs.toast', () => {
    toastElement.remove();
  });
}

// Load bot settings page
async function loadSettings() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  window.state.currentPage = 'settings';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get bot settings
    const response = await axios.get(`${API_BASE_URL}/bot-settings`);
    const { settings } = response.data;

    // Render settings page
    app.innerHTML = `
      <h1 class="mb-4" data-i18n="app.settings.title">Bot Ayarları</h1>

      <form id="settings-form">
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0" data-i18n="app.settings.general">Genel Ayarlar</h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label for="botUsername" class="form-label" data-i18n="app.settings.botUsername">Bot Kullanıcı Adı</label>
              <input type="text" class="form-control" id="botUsername" value="${settings.botUsername}" required>
            </div>

            <div class="mb-3">
              <label for="welcomeMessage" class="form-label" data-i18n="app.settings.welcomeMessage">Karşılama Mesajı</label>
              <textarea class="form-control" id="welcomeMessage" rows="3">${settings.welcomeMessage}</textarea>
              <div class="form-text" data-i18n="app.settings.welcomeMessageHelp">Bu mesaj, kullanıcılar botu ilk kez kullandıklarında gösterilir. Kullanıcıları karşılamak ve bot hakkında bilgi vermek için kullanabilirsiniz.</div>
            </div>

            <div class="mb-3 d-none">
              <label for="shortUrlDomain" class="form-label">Kısa URL Domaini</label>
              <input type="text" class="form-control" id="shortUrlDomain" value="${settings.shortUrlDomain || 'https://t.me'}">
              <div class="form-text">Bildirim mesajlarında kullanılacak kısa URL'lerin domain adresi</div>
            </div>

            <div class="form-check form-switch mb-3 d-none">
              <input class="form-check-input" type="checkbox" id="isActive" ${settings.isActive ? 'checked' : ''}>
              <label class="form-check-label" for="isActive" data-i18n="app.settings.botActive">Bot Aktif</label>
            </div>
          </div>
        </div>

        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0" data-i18n="app.settings.channelRequirement">Kanal Zorunluluğu</h5>
          </div>
          <div class="card-body">
            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="channelRequired" ${settings.channelRequirement.required ? 'checked' : ''}>
              <label class="form-check-label" for="channelRequired" data-i18n="app.contestForm.requireChannel">Kanal Üyeliği Zorunlu</label>
            </div>

            <div class="mb-3">
              <label for="channelUsername" class="form-label" data-i18n="app.contestForm.channelUsername">Kanal Kullanıcı Adı</label>
              <input type="text" class="form-control" id="channelUsername" value="${settings.channelRequirement.channelUsername}">
              <div class="form-text" data-i18n="app.settings.channelUsernameHelp">Gerekirse @ sembolünü ekleyin (örn., @kanaliniz)</div>
            </div>
          </div>
        </div>

        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0" data-i18n="app.settings.maintenanceMode">Bakım Modu</h5>
          </div>
          <div class="card-body">
            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="maintenanceMode" ${settings.maintenanceMode ? 'checked' : ''}>
              <label class="form-check-label" for="maintenanceMode" data-i18n="app.settings.maintenanceMode">Bakım Modu</label>
            </div>

            <div class="mb-3">
              <label for="maintenanceMessage" class="form-label" data-i18n="app.settings.maintenanceMessage">Bakım Mesajı</label>
              <textarea class="form-control" id="maintenanceMessage" rows="3">${settings.maintenanceMessage}</textarea>
            </div>
          </div>
        </div>



        <div id="settings-error" class="alert alert-danger d-none"></div>
        <div id="settings-success" class="alert alert-success d-none"></div>

        <button type="submit" class="btn btn-primary" data-i18n="app.settings.save">Ayarları Kaydet</button>
      </form>
    `;

    // Add event listener to settings form
    document.getElementById('settings-form').addEventListener('submit', handleSaveSettings);

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Settings error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.common.error')}
      </div>
    `;
  }
}

// Handle save settings form submission
async function handleSaveSettings(e) {
  e.preventDefault();

  const errorElement = document.getElementById('settings-error');
  const successElement = document.getElementById('settings-success');

  try {
    errorElement.classList.add('d-none');
    successElement.classList.add('d-none');

    const settings = {
      botUsername: document.getElementById('botUsername').value,
      welcomeMessage: document.getElementById('welcomeMessage').value,
      isActive: document.getElementById('isActive').checked,
      channelRequirement: {
        required: document.getElementById('channelRequired').checked,
        channelUsername: document.getElementById('channelUsername').value,
      },
      maintenanceMode: document.getElementById('maintenanceMode').checked,
      maintenanceMessage: document.getElementById('maintenanceMessage').value,
      shortUrlDomain: document.getElementById('shortUrlDomain').value
    };

    await axios.put(`${API_BASE_URL}/bot-settings`, settings);

    successElement.textContent = i18next.t('app.settings.saveSuccess');
    successElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  } catch (error) {
    console.error('Save settings error:', error);
    errorElement.textContent = error.response?.data?.message || i18next.t('app.common.error');
    errorElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  }
}

// Bildirim otomatik yenileme zamanlayıcısı
let notificationRefreshTimer = null;
const NOTIFICATION_REFRESH_INTERVAL = 15000; // 15 saniye

// Load notifications page
async function loadNotifications() {
  if (!window.state.isAuthenticated) {
    return loadLoginPage();
  }

  // Sayfa değiştiğinde zamanlayıcıyı temizle
  if (window.state.currentPage !== 'notifications') {
    stopNotificationRefresh();
  }

  window.state.currentPage = 'notifications';
  updateNavigation();

  // Load notification modals
  const notificationModalsContainer = document.getElementById('notificationModals');
  if (notificationModalsContainer) {
    try {
      const response = await fetch('/modals/notification-modal.html');
      const modalHtml = await response.text();
      notificationModalsContainer.innerHTML = modalHtml;

      // Initialize modal event listeners
      initNotificationModalListeners();
    } catch (error) {
      console.error('Error loading notification modals:', error);
    }
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Check if admin has permission to send notifications
    const adminResponse = await axios.get(`${API_BASE_URL}/auth/profile`);
    const { admin } = adminResponse.data;
    const canSendNotifications = admin.role === 'superadmin' || (admin.permissions && admin.permissions.sendNotifications);

    // Get daily limit info
    const limitResponse = await axios.get(`${API_BASE_URL}/notifications/limits/daily`);
    const limitInfo = limitResponse.data;

    // Get notifications
    const notificationsResponse = await axios.get(`${API_BASE_URL}/notifications`);
    const { notifications, pagination } = notificationsResponse.data;

    // Render notifications page
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.notifications.title">Bildirimler</h1>
        <div class="d-flex gap-2">
          ${canSendNotifications ? `
            <button class="btn btn-primary" onclick="showCreateNotificationModal()" ${!limitInfo.canSend ? 'disabled' : ''}>
              <i class="fas fa-plus"></i> <span data-i18n="app.notifications.create">Yeni Bildirim</span>
            </button>
          ` : ''}
          <button class="btn btn-secondary" onclick="loadNotifications()">
            <i class="fas fa-sync-alt"></i> <span data-i18n="app.common.refresh">Yenile</span>
          </button>
        </div>
      </div>

      ${!limitInfo.canSend ? `
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle"></i> <span data-i18n="app.notifications.limitReached">Günlük bildirim gönderme limitine ulaşıldı.</span>
          <br>
          <small>Limit: ${limitInfo.limit}, Gönderilen: ${limitInfo.sent}, Kalan: ${limitInfo.remaining}</small>
        </div>
      ` : ''}

      <div class="card mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0" data-i18n="app.notifications.filters">Filtreler</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="statusFilter" data-i18n="app.notifications.status">Durum</label>
                <select class="form-control" id="statusFilter" onchange="filterNotifications()">
                  <option value="" data-i18n="app.common.all">Tümü</option>
                  <option value="DRAFT" data-i18n="app.notifications.statusDraft">Taslak</option>
                  <option value="SENDING" data-i18n="app.notifications.statusSending">Gönderiliyor</option>
                  <option value="PAUSED" data-i18n="app.notifications.statusPaused">Duraklatıldı</option>
                  <option value="COMPLETED" data-i18n="app.notifications.statusCompleted">Tamamlandı</option>
                  <option value="FAILED" data-i18n="app.notifications.statusFailed">Başarısız</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0" data-i18n="app.notifications.list">Bildirim Listesi</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th data-i18n="app.notifications.title">Başlık</th>
                  <th data-i18n="app.notifications.status">Durum</th>
                  <th data-i18n="app.notifications.totalRecipients">Toplam Alıcı</th>
                  <th data-i18n="app.notifications.successCount">Başarılı</th>
                  <th data-i18n="app.notifications.failedCount">Başarısız</th>
                  <th data-i18n="app.notifications.createdAt">Oluşturulma Tarihi</th>
                  <th data-i18n="app.common.actions">İşlemler</th>
                </tr>
              </thead>
              <tbody id="notificationsTableBody">
                ${notifications.length > 0 ? notifications.map(notification => `
                  <tr>
                    <td>${notification.title}</td>
                    <td>${getNotificationStatusBadge(notification.status)}</td>
                    <td>${notification.totalRecipients}</td>
                    <td>${notification.successCount}</td>
                    <td>${notification.failedCount}</td>
                    <td>${new Date(notification.createdAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US')}</td>
                    <td>
                      <div class="btn-group">
                        <button class="btn btn-sm btn-info" onclick="viewNotificationDetails('${notification._id}')">
                          <i class="fas fa-eye"></i>
                        </button>
                        ${notification.status === 'DRAFT' ? `
                          <button class="btn btn-sm btn-primary" onclick="sendNotification('${notification._id}')" ${!limitInfo.canSend ? 'disabled' : ''}>
                            <i class="fas fa-paper-plane"></i>
                          </button>
                        ` : ''}
                        ${notification.status === 'SENDING' ? `
                          <button class="btn btn-sm btn-warning" onclick="pauseNotification('${notification._id}')">
                            <i class="fas fa-pause"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="stopNotification('${notification._id}')">
                            <i class="fas fa-stop"></i>
                          </button>
                        ` : ''}
                        ${notification.status === 'PAUSED' ? `
                          <button class="btn btn-sm btn-success" onclick="resumeNotification('${notification._id}')">
                            <i class="fas fa-play"></i>
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="stopNotification('${notification._id}')">
                            <i class="fas fa-stop"></i>
                          </button>
                        ` : ''}
                        ${notification.status === 'COMPLETED' || notification.status === 'FAILED' ? `
                          <button class="btn btn-sm btn-secondary" onclick="viewNotificationReport('${notification._id}')">
                            <i class="fas fa-chart-bar"></i>
                          </button>
                        ` : ''}
                      </div>
                    </td>
                  </tr>
                `).join('') : `
                  <tr>
                    <td colspan="7" class="text-center" data-i18n="app.notifications.noNotifications">Bildirim bulunamadı</td>
                  </tr>
                `}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-center mt-3">
            <nav aria-label="Bildirim sayfaları">
              <ul class="pagination" id="notificationsPagination">
                <!-- Pagination will be added here -->
              </ul>
            </nav>
          </div>
        </div>
      </div>
    `;

    // Render pagination
    renderPagination(pagination, 'notificationsPagination', 'filterNotifications');

    // Update translations
    updateUI();

    // Otomatik yenileme için kontrol et ve başlat
    checkAndStartNotificationRefresh(notifications);
  } catch (error) {
    console.error('Notifications error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.common.error')}
      </div>
    `;

    // Hata durumunda zamanlayıcıyı durdur
    stopNotificationRefresh();
  }
}

// Bildirim listesinde gönderilen bildirim var mı kontrol et ve otomatik yenilemeyi başlat
function checkAndStartNotificationRefresh(notifications) {
  // Önce mevcut zamanlayıcıyı temizle
  stopNotificationRefresh();

  // Gönderilen bildirim var mı kontrol et (SENDING, PAUSED durumunda olanlar)
  const hasActiveNotifications = notifications.some(notification =>
    ['SENDING', 'PAUSED'].includes(notification.status)
  );

  // Eğer gönderilen bildirim varsa otomatik yenilemeyi başlat
  if (hasActiveNotifications) {
    console.log('Aktif bildirimler bulundu, otomatik yenileme başlatılıyor...');
    startNotificationRefresh();

    // Kullanıcıya bilgi ver (sadece ilk kez)
    if (!sessionStorage.getItem('autoRefreshNotified')) {
      const refreshInfoElement = document.createElement('div');
      refreshInfoElement.className = 'alert alert-info alert-dismissible fade show mt-2';
      refreshInfoElement.innerHTML = `
        <i class="fas fa-sync-alt fa-spin mr-2"></i>
        <span>${i18next.t('app.notifications.autoRefresh', { seconds: NOTIFICATION_REFRESH_INTERVAL / 1000 })}</span>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      `;

      const alertsContainer = document.querySelector('.alerts-container');
      if (alertsContainer) {
        alertsContainer.appendChild(refreshInfoElement);
      } else {
        const container = document.createElement('div');
        container.className = 'alerts-container';
        container.appendChild(refreshInfoElement);
        app.insertBefore(container, app.firstChild);
      }

      sessionStorage.setItem('autoRefreshNotified', 'true');
    }
  } else {
    console.log('Aktif bildirim bulunamadı, otomatik yenileme durduruldu.');
  }
}

// Otomatik yenilemeyi başlat
function startNotificationRefresh() {
  if (!notificationRefreshTimer) {
    notificationRefreshTimer = setInterval(() => {
      console.log('Bildirimler otomatik olarak yenileniyor...');
      const statusFilter = document.getElementById('statusFilter');
      const status = statusFilter ? statusFilter.value : '';

      // Mevcut sayfada kalarak bildirimleri yenile
      filterNotifications(1, status);
    }, NOTIFICATION_REFRESH_INTERVAL);

    console.log(`Otomatik yenileme başlatıldı (${NOTIFICATION_REFRESH_INTERVAL / 1000} saniye aralıkla)`);
  }
}

// Otomatik yenilemeyi durdur
function stopNotificationRefresh() {
  if (notificationRefreshTimer) {
    clearInterval(notificationRefreshTimer);
    notificationRefreshTimer = null;
    console.log('Otomatik yenileme durduruldu');
  }
}

// Get notification status badge
function getNotificationStatusBadge(status) {
  const statusMap = {
    'DRAFT': '<span class="badge bg-secondary">Taslak</span>',
    'SENDING': '<span class="badge bg-primary">Gönderiliyor</span>',
    'PAUSED': '<span class="badge bg-warning">Duraklatıldı</span>',
    'COMPLETED': '<span class="badge bg-success">Tamamlandı</span>',
    'FAILED': '<span class="badge bg-danger">Başarısız</span>'
  };

  return statusMap[status] || `<span class="badge bg-secondary">${status}</span>`;
}

// Filter notifications
async function filterNotifications(page = 1) {
  const statusFilter = document.getElementById('statusFilter').value;

  try {
    // Show loading
    document.getElementById('notificationsTableBody').innerHTML = `
      <tr>
        <td colspan="7" class="text-center">
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
          </div>
        </td>
      </tr>
    `;

    // Get notifications with filters
    const response = await axios.get(`${API_BASE_URL}/notifications`, {
      params: {
        status: statusFilter,
        page,
        limit: 10
      }
    });

    const { notifications, pagination } = response.data;

    // Update table
    document.getElementById('notificationsTableBody').innerHTML = notifications.length > 0 ? notifications.map(notification => `
      <tr>
        <td>${notification.title}</td>
        <td>${getNotificationStatusBadge(notification.status)}</td>
        <td>${notification.totalRecipients}</td>
        <td>${notification.successCount}</td>
        <td>${notification.failedCount}</td>
        <td>${new Date(notification.createdAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US')}</td>
        <td>
          <div class="btn-group">
            <button class="btn btn-sm btn-info" onclick="viewNotificationDetails('${notification._id}')">
              <i class="fas fa-eye"></i>
            </button>
            ${notification.status === 'DRAFT' ? `
              <button class="btn btn-sm btn-primary" onclick="sendNotification('${notification._id}')">
                <i class="fas fa-paper-plane"></i>
              </button>
            ` : ''}
            ${notification.status === 'SENDING' ? `
              <button class="btn btn-sm btn-warning" onclick="pauseNotification('${notification._id}')">
                <i class="fas fa-pause"></i>
              </button>
              <button class="btn btn-sm btn-danger" onclick="stopNotification('${notification._id}')">
                <i class="fas fa-stop"></i>
              </button>
            ` : ''}
            ${notification.status === 'PAUSED' ? `
              <button class="btn btn-sm btn-success" onclick="resumeNotification('${notification._id}')">
                <i class="fas fa-play"></i>
              </button>
              <button class="btn btn-sm btn-danger" onclick="stopNotification('${notification._id}')">
                <i class="fas fa-stop"></i>
              </button>
            ` : ''}
            ${notification.status === 'COMPLETED' || notification.status === 'FAILED' ? `
              <button class="btn btn-sm btn-secondary" onclick="viewNotificationReport('${notification._id}')">
                <i class="fas fa-chart-bar"></i>
              </button>
              ${notification.failedCount > 999 ? `
              <button class="btn btn-sm btn-warning ${(notification.retryCount || 0) >= 3 ? 'disabled' : ''}"
                onclick="${(notification.retryCount || 0) >= 3 ? 'return false' : `retryFailedNotification('${notification._id}')`}"
                title="Başarısız bildirimleri yeniden gönder${notification.retryCount ? ` (${notification.retryCount}/3)` : ''}">
                <i class="fas fa-redo-alt"></i>${notification.retryCount ? ` ${notification.retryCount}/3` : ''}
              </button>
              ` : ''}
              ${notification.status === 'FAILED' ? `
              <button class="btn btn-sm btn-primary ${(notification.retryCount || 0) >= 3 ? 'disabled' : ''}"
                onclick="${(notification.retryCount || 0) >= 3 ? 'return false' : `resendNotification('${notification._id}')`}"
                title="Bildirimi yeniden gönder${notification.retryCount ? ` (${notification.retryCount}/3)` : ''}">
                <i class="fas fa-paper-plane"></i>${notification.retryCount ? ` ${notification.retryCount}/3` : ''}
              </button>
              ` : ''}
            ` : ''}
          </div>
        </td>
      </tr>
    `).join('') : `
      <tr>
        <td colspan="7" class="text-center" data-i18n="app.notifications.noNotifications">Bildirim bulunamadı</td>
      </tr>
    `;

    // Update pagination
    renderPagination(pagination, 'notificationsPagination', 'filterNotifications');

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Filter notifications error:', error);
    document.getElementById('notificationsTableBody').innerHTML = `
      <tr>
        <td colspan="7" class="text-center text-danger">
          ${i18next.t('app.common.error')}
        </td>
      </tr>
    `;
  }
}

// View user details
async function viewUser(userId) {
  if (!window.state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get user details
    const response = await axios.get(`${API_BASE_URL}/users/${userId}`);
    const { user, submissions } = response.data;

    // Render user details
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.userDetail.title">Kullanıcı Detayları</h1>
        <button class="btn btn-secondary" onclick="loadUsers()" data-i18n="app.userDetail.back">Kullanıcı Listesine Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0" data-i18n="app.userDetail.info">Kullanıcı Bilgileri</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong data-i18n="app.userDetail.username">Kullanıcı Adı:</strong> ${user.username}</p>
              <p><strong data-i18n="app.userDetail.telegramId">Telegram ID:</strong> ${user.telegramId}</p>
              <p><strong data-i18n="app.userDetail.name">İsim:</strong> ${user.firstName || '-'} ${user.lastName || ''}</p>
              <p>
                <strong data-i18n="app.userDetail.blockStatus">Blok Durumu:</strong>
                <span class="badge ${user.isBlocked ? 'bg-danger' : 'bg-success'}">
                  ${user.isBlocked ? i18next.t('app.userDetail.blocked') : i18next.t('app.userDetail.notBlocked')}
                </span>
              </p>
              ${user.isBlocked ? `
                ${user.blockReason ? `<p><strong data-i18n="app.userDetail.blockReason">Blok Nedeni:</strong> ${user.blockReason}</p>` : ''}
                ${user.blockedBy ? `<p><strong data-i18n="app.userDetail.blockedBy">Bloklayan:</strong> ${user.blockedBy.name} (${user.blockedBy.username})</p>` : ''}
                ${user.blockedAt ? `<p><strong data-i18n="app.userDetail.blockedAt">Bloklanma Tarihi:</strong> ${new Date(user.blockedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>` : ''}
              ` : ''}
            </div>
            <div class="col-md-6">
              <p><strong data-i18n="app.userDetail.registrationDate">Kayıt Tarihi:</strong> ${new Date(user.registrationDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric' })}</p>
              <p><strong data-i18n="app.userDetail.lastActivity">Son Aktivite:</strong> ${new Date(user.lastActivity).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric' })}</p>
              <p><strong data-i18n="app.userDetail.status">Durum:</strong> ${user.state}</p>
              <p><strong data-i18n="app.userDetail.language">Dil:</strong> ${user.language}</p>
            </div>
          </div>

          <div class="mt-3">
            ${user.isBlocked ? `
              <button class="btn btn-success" onclick="unblockUser('${user._id}')" data-i18n="app.userDetail.unblock">Blokajı Kaldır</button>
            ` : `
              <button class="btn btn-danger" onclick="showBlockUserModal('${user._id}')" data-i18n="app.userDetail.block">Kullanıcıyı Blokla</button>
            `}
          </div>
        </div>
      </div>

      <h2 class="mb-3" data-i18n="app.userDetail.submissions">Katılımlar</h2>

      ${submissions.length > 0 ? `
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th data-i18n="app.contestForm.title">Yarışma</th>
                <th data-i18n="app.contests.status">Durum</th>
                <th data-i18n="app.userDetail.submissionDate">Gönderim Tarihi</th>
                <th data-i18n="app.userDetail.correctAnswers">Doğru Cevaplar</th>
                <th data-i18n="app.userDetail.winner">Kazanan</th>
                <th data-i18n="app.contests.actions">İşlemler</th>
              </tr>
            </thead>
            <tbody>
              ${submissions.map(submission => `
                <tr>
                  <td>${submission.contest.title}</td>
                  <td>${submission.contest.status}</td>
                  <td>${new Date(submission.submittedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                  <td>${submission.correctAnswers !== undefined ? submission.correctAnswers : '-'}</td>
                  <td>${submission.isWinner ? i18next.t('app.common.yes') : i18next.t('app.common.no')}</td>
                  <td>
                    <a href="#" onclick="viewContest('${submission.contest._id}')" class="btn btn-sm btn-primary" data-i18n="app.contests.view">Yarışmayı Görüntüle</a>
                    <button class="btn btn-sm btn-info" onclick="viewSubmissionAnswers('${submission._id}')" data-i18n="app.submissions.viewAnswers">Cevapları Görüntüle</button>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      ` : `
        <div class="alert alert-info">
          ${i18next.t('app.userDetail.noSubmissions')}
        </div>
      `}
    `;

    // Update translations
    updateUI();
  } catch (error) {
    console.error('User details error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.common.error')}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadUsers()" data-i18n="app.userDetail.back">Kullanıcı Listesine Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// View contest details
async function viewContest(contestId) {
  if (!window.state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get contest details
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = response.data;

    // Render contest details
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.contestDetail.title">Yarışma Detayları</h1>
        <button class="btn btn-secondary" onclick="loadContests()" data-i18n="app.contestDetail.back">Yarışma Listesine Dön</button>
      </div>

      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">${contest.title}</h5>
          <span class="badge ${getBadgeClass(contest.status)}">${contest.status}</span>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-6">
              <p><strong data-i18n="app.contests.startDate">Başlangıç Tarihi:</strong> ${new Date(contest.startDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>
              <p><strong data-i18n="app.contests.endDate">Bitiş Tarihi:</strong> ${new Date(contest.endDate).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</p>
              <p><strong data-i18n="app.contests.creator">Oluşturan:</strong> ${contest.createdBy.name}</p>
              <p><strong data-i18n="app.contests.participants">Katılımcılar:</strong> ${contest.participantCount || 0}</p>
              ${contest.status === 'COMPLETED' ? `<p><strong data-i18n="app.contests.winners">Kazananlar:</strong> ${contest.winnerCount || 0}</p>` : ''}
            </div>
            <div class="col-md-6">
              <p><strong data-i18n="app.contestDetail.questionCount">Soru Sayısı:</strong> ${contest.questions.length}</p>
              <p><strong data-i18n="app.contestDetail.minCorrectAnswers">Kazanmak için Gereken Doğru Cevap:</strong> ${contest.minCorrectAnswers}</p>
              <p><strong data-i18n="app.contestDetail.channelRequirement">Kanal Zorunluluğu:</strong> ${contest.channelRequirement.required ? contest.channelRequirement.channelUsername : i18next.t('app.common.none')}</p>
            </div>
          </div>

          <div class="mb-3">
            <p><strong data-i18n="app.contestDetail.description">Açıklama:</strong> ${contest.description}</p>
          </div>

          ${contest.requirements ? `
            <div class="mb-3">
              <p><strong data-i18n="app.contestDetail.requirements">Katılım Şartları:</strong> ${contest.requirements}</p>
            </div>
          ` : ''}

          ${contest.prizes ? `
            <div class="mb-3">
              <p><strong data-i18n="app.contestDetail.prizes">Ödüller:</strong> ${contest.prizes}</p>
            </div>
          ` : ''}
        </div>
      </div>

      <h2 class="mb-3" data-i18n="app.contestDetail.questions">Sorular</h2>

      <div class="accordion mb-4" id="questionsAccordion">
        ${contest.questions.map((question, index) => `
          <div class="accordion-item">
            <h2 class="accordion-header">
              <button class="accordion-button ${index > 0 ? 'collapsed' : ''}" type="button" data-bs-toggle="collapse" data-bs-target="#question${index}">
                ${i18next.t('app.contestDetail.question')} ${index + 1}: ${question.text}
              </button>
            </h2>
            <div id="question${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" data-bs-parent="#questionsAccordion">
              <div class="accordion-body">
                <h6 data-i18n="app.contestDetail.options">Seçenekler:</h6>
                <ul class="list-group mb-3">
                  ${question.options.map(option => `
                    <li class="list-group-item ${question.correctAnswer === option.value ? 'list-group-item-success' : ''}">
                      ${option.text} ${question.correctAnswer === option.value ? `<span class="badge bg-success float-end" data-i18n="app.contestDetail.correctAnswer">Doğru Cevap</span>` : ''}
                    </li>
                  `).join('')}
                </ul>
              </div>
            </div>
          </div>
        `).join('')}
      </div>

      <div class="d-flex gap-2 mb-4">
        ${contest.status !== 'COMPLETED' && contest.status !== 'CANCELLED' ? `
          <button class="btn btn-warning" onclick="editContest('${contest._id}')" data-i18n="app.contests.edit">Düzenle</button>
          ${contest.status === 'ACTIVE' ? `
            <button class="btn btn-info" onclick="setCorrectAnswers('${contest._id}')" data-i18n="app.contestDetail.setAnswers">Doğru Cevapları Belirle</button>
            <button class="btn btn-success" onclick="completeContest('${contest._id}')" data-i18n="app.contestDetail.complete">Yarışmayı Tamamla</button>
            <button class="btn btn-danger" onclick="showCancelContestModal('${contest._id}')" data-i18n="app.contests.cancel">Yarışmayı İptal Et</button>
            <a href="#" onclick="viewSubmissions('${contest._id}')" class="btn btn-info" data-i18n="app.contestDetail.submissions">Katılımlar</a>
          ` : ''}
        ` : ''}

        ${contest.status === 'COMPLETED' ? `
          <button class="btn btn-info" onclick="viewSubmissions('${contest._id}')" data-i18n="app.contestDetail.submissions">Katılımları Görüntüle</button>
          <button onclick="exportToExcel('${contestId}')" class="btn btn-success me-2" data-i18n="app.contestDetail.export">Excel'e Aktar</button>
          <button class="btn btn-primary" onclick="sendNotificationToWinners('${contest._id}')" data-i18n="app.contestDetail.notifyWinners">Kazananlara Bildirim Gönder</button>
        ` : ''}

        ${contest.status === 'CANCELLED' ? `
          <button class="btn btn-info" onclick="viewSubmissions('${contest._id}')" data-i18n="app.contestDetail.submissions">Katılımları Görüntüle</button>
          ${contest.cancelReason ? `
            <div class="alert alert-warning mt-3">
              <strong data-i18n="app.contests.cancelReason">İptal Nedeni:</strong> ${contest.cancelReason}
              ${contest.cancelledBy ? `<br><small data-i18n="app.contests.cancelledBy">İptal Eden:</small> ${contest.cancelledBy.name}` : ''}
              ${contest.cancelledAt ? `<br><small data-i18n="app.contests.cancelledAt">İptal Tarihi:</small> ${new Date(contest.cancelledAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}` : ''}
            </div>
          ` : ''}
        ` : ''}
      </div>
    `;

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Contest details error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.common.error')}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="loadContests()" data-i18n="app.contestDetail.back">Yarışma Listesine Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// Create new contest
function loadCreateContest() {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  state.currentPage = 'create-contest';
  updateNavigation();

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  // Get bot settings to check channel requirement
  axios.get(`${API_BASE_URL}/bot-settings`)
    .then(response => {
      const { settings } = response.data;

      const channelRequired = settings.channelRequirement?.required || false;
      const channelUsername = settings.channelRequirement?.channelUsername || '';

      app.innerHTML = `
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1 data-i18n="app.contestForm.createTitle">Yeni Yarışma Oluştur</h1>
      <button class="btn btn-secondary" onclick="loadContests()" data-i18n="app.contestDetail.back">Yarışma Listesine Dön</button>
    </div>

    <form id="contest-form">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0" data-i18n="app.contestForm.contestInfo">Yarışma Bilgileri</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="contestType" class="form-label" data-i18n="app.contestForm.contestType">Yarışma Tipi</label>
            <select class="form-select" id="contestType" name="contestType" required>
              <option value="SPORTS_PREDICTION" data-i18n="app.contestForm.sportsPrediction">🏆 Spor Tahmin Yarışması</option>
              <option value="EMOJI_GAME" data-i18n="app.contestForm.emojiGame">🎮 Emoji Yarışması</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="title" class="form-label" data-i18n="app.contestForm.title">Başlık</label>
            <input type="text" class="form-control" id="title" name="title" required>
          </div>

          <div class="mb-3">
            <label for="description" class="form-label" data-i18n="app.contestForm.description">Açıklama</label>
            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="startDate" class="form-label" data-i18n="app.contestForm.startDate">Başlangıç Tarihi</label>
                <input type="datetime-local" class="form-control" id="startDate" name="startDate" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="endDate" class="form-label" data-i18n="app.contestForm.endDate">Bitiş Tarihi</label>
                <input type="datetime-local" class="form-control" id="endDate" name="endDate" required>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="requirements" class="form-label"><span data-i18n="app.contestForm.requirements">Katılım Şartları</span> <span data-i18n="app.contestForm.optional"></span></label>
            <textarea class="form-control" id="requirements" name="requirements" rows="2"></textarea>
          </div>

          <div class="mb-3">
            <label for="prizes" class="form-label"><span data-i18n="app.contestForm.prizes">Ödüller</span> <span data-i18n="app.contestForm.optional"></span></label>
            <textarea class="form-control" id="prizes" name="prizes" rows="2"></textarea>
          </div>

          <div class="mb-3" id="minCorrectAnswersContainer">
            <label for="minCorrectAnswers" class="form-label" data-i18n="app.contestDetail.minCorrectAnswers">Kazanmak için Gereken Minimum Doğru Cevap Sayısı</label>
            <input type="number" class="form-control" id="minCorrectAnswers" name="minCorrectAnswers" min="1" value="1" required>
          </div>

          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="channelRequired" name="channelRequired" ${channelRequired ? 'checked' : ''}>
            <label class="form-check-label" for="channelRequired" data-i18n="app.contestForm.requireChannel">Kanal Üyeliği Zorunlu</label>
          </div>

          <div class="mb-3" id="channelUsernameContainer" style="display: ${channelRequired ? 'block' : 'none'};">
            <label for="channelUsername" class="form-label" data-i18n="app.contestForm.channelUsername">Kanal Kullanıcı Adı</label>
            <input type="text" class="form-control" id="channelUsername" name="channelUsername" placeholder="@kanaladi" value="${channelUsername}">
          </div>
        </div>
      </div>

      <!-- Sports Prediction Settings -->
      <div class="card mb-4" id="sportsSettings">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0" data-i18n="app.contestDetail.questions">Sorular</h5>
          <button type="button" class="btn btn-sm btn-primary" id="addQuestionBtn" data-i18n="app.contestForm.addQuestion">Soru Ekle</button>
        </div>
        <div class="card-body">
          <div id="questionsContainer">
            <!-- Questions will be added here -->
          </div>

          <div class="alert alert-info" id="noQuestionsAlert">
            <span data-i18n="app.contestForm.noQuestions">Henüz soru eklenmedi. Soru eklemek için "Soru Ekle" butonuna tıklayın.</span>
          </div>
        </div>
      </div>

      <!-- Emoji Game Settings -->
      <div class="card mb-4" id="emojiSettings" style="display: none;">
        <div class="card-header">
          <h5 class="mb-0" data-i18n="app.contestForm.emojiGameSettings">🎮 Emoji Yarışması Ayarları</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label for="gameType" class="form-label" data-i18n="app.contestForm.gameType">Oyun Tipi</label>
            <select class="form-select" id="gameType" name="gameType" required>
              <option value="">Oyun tipi seçin...</option>
              <option value="DICE">🎲 Zar Oyunu</option>
              <option value="BASKETBALL">🏀 Basketbol</option>
              <option value="FOOTBALL">⚽ Futbol (Penaltı)</option>
              <option value="DART">🎯 Dart</option>
              <option value="BOWLING">🎳 Bowling</option>
              <option value="SLOT">🎰 Slot Makinesi</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="attemptCount" class="form-label" data-i18n="app.contestForm.attemptCount">Deneme Sayısı</label>
            <input type="number" class="form-control" id="attemptCount" name="attemptCount" min="1" max="50" value="5" required>
            <div class="form-text" data-i18n="app.contestForm.attemptCountHelp">Kullanıcıların kaç deneme yapabileceğini belirler (1-50 arası)</div>
          </div>

          <!-- Dice Settings -->
          <div id="diceSettings" style="display: none;">
            <h6 data-i18n="app.contestForm.diceSettings">🎲 Zar Ayarları</h6>
            <div class="mb-3">
              <label for="diceTargetType" class="form-label" data-i18n="app.contestForm.targetType">Hedef Tipi</label>
              <select class="form-select" id="diceTargetType" name="diceTargetType">
                <option value="TOTAL_VALUE">Toplam Değer</option>
                <option value="SPECIFIC_VALUE_COUNT">Belirli Değer Sayısı</option>
              </select>
            </div>
            <div class="mb-3" id="totalValueContainer">
              <label for="targetValue" class="form-label" data-i18n="app.contestForm.targetValue">Hedef Toplam Değer</label>
              <input type="number" class="form-control" id="targetValue" name="targetValue" min="1" max="300" value="20">
              <div class="form-text">Kullanıcıların ulaşması gereken minimum toplam puan</div>
            </div>
            <div class="mb-3" id="specificValueContainer" style="display: none;">
              <div class="row">
                <div class="col-md-6">
                  <label for="specificValue" class="form-label" data-i18n="app.contestForm.specificValue">Hedef Zar Değeri</label>
                  <select class="form-select" id="specificValue" name="specificValue">
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label for="targetCount" class="form-label" data-i18n="app.contestForm.targetCount">Hedef Sayı</label>
                  <input type="number" class="form-control" id="targetCount" name="targetCount" min="1" max="50" value="2">
                  <div class="form-text">Kaç adet bu değerin atılması gerektiği</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Basketball/Football Settings -->
          <div id="sportSettings" style="display: none;">
            <h6 id="sportSettingsTitle">🏀 Basketbol Ayarları</h6>
            <div class="mb-3">
              <label for="successTarget" class="form-label" data-i18n="app.contestForm.successTarget">Başarı Hedefi</label>
              <input type="number" class="form-control" id="successTarget" name="successTarget" min="1" max="50" value="3">
              <div class="form-text" id="successTargetHelp">Kaç başarılı atış yapılması gerektiği</div>
            </div>
          </div>

          <!-- Dart Settings -->
          <div id="dartSettings" style="display: none;">
            <h6 data-i18n="app.contestForm.dartSettings">🎯 Dart Ayarları</h6>
            <div class="mb-3">
              <label for="bullseyeTarget" class="form-label" data-i18n="app.contestForm.bullseyeTarget">Bullseye Hedefi</label>
              <input type="number" class="form-control" id="bullseyeTarget" name="bullseyeTarget" min="1" max="50" value="2">
              <div class="form-text">Kaç adet tam onikiden (bullseye) vurulması gerektiği</div>
            </div>
          </div>

          <!-- Bowling Settings -->
          <div id="bowlingSettings" style="display: none;">
            <h6 data-i18n="app.contestForm.bowlingSettings">🎳 Bowling Ayarları</h6>
            <div class="mb-3">
              <label for="strikeTarget" class="form-label" data-i18n="app.contestForm.strikeTarget">Strike Hedefi</label>
              <input type="number" class="form-control" id="strikeTarget" name="strikeTarget" min="1" max="50" value="2">
              <div class="form-text">Kaç adet strike atılması gerektiği</div>
            </div>
          </div>

          <!-- Slot Settings -->
          <div id="slotSettings" style="display: none;">
            <h6 data-i18n="app.contestForm.slotSettings">🎰 Slot Makinesi Ayarları</h6>
            <div class="mb-3">
              <label class="form-label" data-i18n="app.contestForm.winningCombinations">Kazanan Kombinasyonlar</label>
              <div id="slotCombinations">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="🍒🍒🍒" id="cherry" name="slotCombinations">
                  <label class="form-check-label" for="cherry">🍒🍒🍒 - Üç Kiraz</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="🔔🔔🔔" id="bell" name="slotCombinations">
                  <label class="form-check-label" for="bell">🔔🔔🔔 - Üç Çan</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="➖➖➖" id="bar" name="slotCombinations">
                  <label class="form-check-label" for="bar">➖➖➖ - Üç Bar</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="7️⃣7️⃣7️⃣" id="seven" name="slotCombinations">
                  <label class="form-check-label" for="seven">7️⃣7️⃣7️⃣ - Üç Yedi</label>
                </div>
              </div>
              <div class="form-text">En az bir kazanan kombinasyon seçmelisiniz</div>
            </div>
          </div>
        </div>
      </div>

      <div id="contest-error" class="alert alert-danger d-none"></div>

      <div class="d-flex gap-2">
        <button type="submit" class="btn btn-primary" data-i18n="app.contestForm.createContest">Yarışmayı Oluştur</button>
        <button type="button" class="btn btn-secondary" onclick="loadContests()" data-i18n="app.contestForm.cancel">İptal</button>
      </div>
    </form>
  `;

      // Add event listeners
      document.getElementById('channelRequired').addEventListener('change', function () {
        document.getElementById('channelUsernameContainer').style.display = this.checked ? 'block' : 'none';
      });

      // Contest type change handler
      document.getElementById('contestType').addEventListener('change', function () {
        const contestType = this.value;
        const sportsSettings = document.getElementById('sportsSettings');
        const emojiSettings = document.getElementById('emojiSettings');
        const minCorrectAnswersContainer = document.getElementById('minCorrectAnswersContainer');

        if (contestType === 'SPORTS_PREDICTION') {
          sportsSettings.style.display = 'block';
          emojiSettings.style.display = 'none';
          minCorrectAnswersContainer.style.display = 'block';

          // Enable required attributes for sports prediction
          document.getElementById('minCorrectAnswers').required = true;
          document.querySelectorAll('.question-text').forEach(input => input.required = true);
          document.querySelectorAll('.option-text').forEach(input => input.required = true);

          // Disable required attributes for emoji game
          const gameTypeSelect = document.getElementById('gameType');
          if (gameTypeSelect) gameTypeSelect.required = false;
          const attemptCountInput = document.getElementById('attemptCount');
          if (attemptCountInput) attemptCountInput.required = false;
        } else if (contestType === 'EMOJI_GAME') {
          sportsSettings.style.display = 'none';
          emojiSettings.style.display = 'block';
          minCorrectAnswersContainer.style.display = 'none';

          // Disable required attributes for sports prediction
          const minCorrectAnswersInput = document.getElementById('minCorrectAnswers');
          if (minCorrectAnswersInput) minCorrectAnswersInput.required = false;
          document.querySelectorAll('.question-text').forEach(input => input.required = false);
          document.querySelectorAll('.option-text').forEach(input => input.required = false);

          // Enable required attributes for emoji game
          document.getElementById('gameType').required = true;
          document.getElementById('attemptCount').required = true;
        }
      });

      // Game type change handler
      document.getElementById('gameType').addEventListener('change', function () {
        const gameType = this.value;

        // Hide all game-specific settings
        document.getElementById('diceSettings').style.display = 'none';
        document.getElementById('sportSettings').style.display = 'none';
        document.getElementById('dartSettings').style.display = 'none';
        document.getElementById('bowlingSettings').style.display = 'none';
        document.getElementById('slotSettings').style.display = 'none';

        // Show relevant settings
        switch (gameType) {
          case 'DICE':
            document.getElementById('diceSettings').style.display = 'block';
            break;
          case 'BASKETBALL':
            document.getElementById('sportSettings').style.display = 'block';
            document.getElementById('sportSettingsTitle').textContent = '🏀 Basketbol Ayarları';
            document.getElementById('successTargetHelp').textContent = 'Kaç başarılı basketbol atışı yapılması gerektiği';
            break;
          case 'FOOTBALL':
            document.getElementById('sportSettings').style.display = 'block';
            document.getElementById('sportSettingsTitle').textContent = '⚽ Futbol Ayarları';
            document.getElementById('successTargetHelp').textContent = 'Kaç başarılı penaltı atışı yapılması gerektiği';
            break;
          case 'DART':
            document.getElementById('dartSettings').style.display = 'block';
            break;
          case 'BOWLING':
            document.getElementById('bowlingSettings').style.display = 'block';
            break;
          case 'SLOT':
            document.getElementById('slotSettings').style.display = 'block';
            break;
        }
      });

      // Dice target type change handler
      document.getElementById('diceTargetType').addEventListener('change', function () {
        const targetType = this.value;
        const totalValueContainer = document.getElementById('totalValueContainer');
        const specificValueContainer = document.getElementById('specificValueContainer');

        if (targetType === 'TOTAL_VALUE') {
          totalValueContainer.style.display = 'block';
          specificValueContainer.style.display = 'none';
        } else if (targetType === 'SPECIFIC_VALUE_COUNT') {
          totalValueContainer.style.display = 'none';
          specificValueContainer.style.display = 'block';
        }
      });

      document.getElementById('addQuestionBtn').addEventListener('click', addQuestion);
      document.getElementById('contest-form').addEventListener('submit', handleCreateContest);

      // Add first question by default for sports prediction
      addQuestion();

      // Initialize required attributes based on default contest type (SPORTS_PREDICTION)
      document.getElementById('gameType').required = false;
      document.getElementById('attemptCount').required = false;
    })
    .catch(error => {
      console.error('Error loading bot settings:', error);
      app.innerHTML = `
        <div class="alert alert-danger">
          ${i18next.t('app.common.error')}
        </div>
      `;
    });
}

// Add question to form
function addQuestion() {
  const questionsContainer = document.getElementById('questionsContainer');
  const noQuestionsAlert = document.getElementById('noQuestionsAlert');

  // Hide no questions alert if it exists
  if (noQuestionsAlert) {
    noQuestionsAlert.style.display = 'none';
  }

  // Create question index
  const questionIndex = questionsContainer.children.length;

  // Create question element
  const questionElement = document.createElement('div');
  questionElement.className = 'card mb-3 question-card';
  questionElement.dataset.index = questionIndex;

  questionElement.innerHTML = `
    <div class="card-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0"><span data-i18n="app.contestDetail.question">Soru</span> ${questionIndex + 1}</h6>
      <button type="button" class="btn btn-sm btn-danger remove-question-btn" data-i18n="app.contestForm.removeQuestion">Kaldır</button>
    </div>
    <div class="card-body">
      <div class="mb-3">
        <label class="form-label" data-i18n="app.contestForm.questionText">Soru Metni</label>
        <input type="text" class="form-control question-text" name="questionText" required>
      </div>

      <div class="options-container mb-3">
        <!-- Options will be added here -->
      </div>

      <button type="button" class="btn btn-sm btn-success add-option-btn" data-i18n="app.contestForm.addOption">Seçenek Ekle</button>
    </div>
  `;

  // Add question to container
  questionsContainer.appendChild(questionElement);

  // Set required attribute based on current contest type
  const contestType = document.getElementById('contestType')?.value;
  const questionTextInput = questionElement.querySelector('.question-text');
  if (contestType === 'SPORTS_PREDICTION') {
    questionTextInput.required = true;
  } else {
    questionTextInput.required = false;
  }

  // Add event listeners
  questionElement.querySelector('.remove-question-btn').addEventListener('click', function () {
    questionElement.remove();
    updateQuestionNumbers();

    // Show no questions alert if no questions and alert exists
    if (questionsContainer.children.length === 0 && noQuestionsAlert) {
      noQuestionsAlert.style.display = 'block';
    }
  });

  questionElement.querySelector('.add-option-btn').addEventListener('click', function () {
    addOption(questionElement);
  });

  // Add two options by default
  addOption(questionElement);
  addOption(questionElement);
}

// Add option to question
function addOption(questionElement) {
  const optionsContainer = questionElement.querySelector('.options-container');

  // Create option index
  const optionIndex = optionsContainer.children.length;

  // Create option element
  const optionElement = document.createElement('div');
  optionElement.className = 'input-group mb-2 option-group';
  optionElement.dataset.index = optionIndex;

  optionElement.innerHTML = `
    <span class="input-group-text"><span data-i18n="app.contestForm.option">Seçenek</span> ${String.fromCharCode(65 + optionIndex)}</span>
    <input type="text" class="form-control option-text" name="optionText" required>
    <button type="button" class="btn btn-outline-danger remove-option-btn" data-i18n="app.contestForm.removeOption">Kaldır</button>
  `;

  // Add option to container
  optionsContainer.appendChild(optionElement);

  // Set required attribute based on current contest type
  const contestType = document.getElementById('contestType')?.value;
  const optionTextInput = optionElement.querySelector('.option-text');
  if (contestType === 'SPORTS_PREDICTION') {
    optionTextInput.required = true;
  } else {
    optionTextInput.required = false;
  }

  // Add event listener
  optionElement.querySelector('.remove-option-btn').addEventListener('click', function () {
    optionElement.remove();
    updateOptionLetters(questionElement);
  });
}

// Update question numbers
function updateQuestionNumbers() {
  const questionCards = document.querySelectorAll('.question-card');

  questionCards.forEach((card, index) => {
    card.dataset.index = index;
    card.querySelector('h6').innerHTML = `<span data-i18n="app.contestDetail.question">Soru</span> ${index + 1}`;
  });
}

// Update option letters
function updateOptionLetters(questionElement) {
  const optionGroups = questionElement.querySelectorAll('.option-group');

  optionGroups.forEach((group, index) => {
    group.dataset.index = index;
    group.querySelector('.input-group-text').innerHTML = `<span data-i18n="app.contestForm.option">Seçenek</span> ${String.fromCharCode(65 + index)}`;
  });
}

// Render pagination
function renderPagination(pagination, elementId, callbackName) {
  const paginationElement = document.getElementById(elementId);
  if (!paginationElement) return;

  paginationElement.innerHTML = '';

  if (!pagination || pagination.total <= pagination.limit) return;

  const totalPages = pagination.pages;
  const currentPage = parseInt(pagination.page);

  // Previous page
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `
    <a class="page-link" href="#" onclick="${callbackName}(${currentPage - 1}); return false;">
      <i class="fas fa-chevron-left"></i>
    </a>
  `;
  paginationElement.appendChild(prevLi);

  // Page numbers
  for (let i = 1; i <= totalPages; i++) {
    if (
      i === 1 ||
      i === totalPages ||
      (i >= currentPage - 1 && i <= currentPage + 1)
    ) {
      const li = document.createElement('li');
      li.className = `page-item ${i === currentPage ? 'active' : ''}`;
      li.innerHTML = `
        <a class="page-link" href="#" onclick="${callbackName}(${i}); return false;">${i}</a>
      `;
      paginationElement.appendChild(li);
    } else if (
      i === currentPage - 2 ||
      i === currentPage + 2
    ) {
      const li = document.createElement('li');
      li.className = 'page-item disabled';
      li.innerHTML = '<a class="page-link" href="#">...</a>';
      paginationElement.appendChild(li);
    }
  }

  // Next page
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `
    <a class="page-link" href="#" onclick="${callbackName}(${currentPage + 1}); return false;">
      <i class="fas fa-chevron-right"></i>
    </a>
  `;
  paginationElement.appendChild(nextLi);
}

// Handle create contest form submission
async function handleCreateContest(e) {
  e.preventDefault();

  const errorElement = document.getElementById('contest-error');
  errorElement.classList.add('d-none');

  try {
    // Validate dates
    const startDateObj = new Date(document.getElementById('startDate').value);
    const endDateObj = new Date(document.getElementById('endDate').value);

    if (endDateObj <= startDateObj) {
      throw new Error(i18next.t('app.contestForm.dateError'));
    }

    // Get form data
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const requirements = document.getElementById('requirements').value;
    const prizes = document.getElementById('prizes').value;
    const contestType = document.getElementById('contestType').value;
    const channelRequired = document.getElementById('channelRequired').checked;
    const channelUsername = document.getElementById('channelUsername').value;

    // Create contest data
    const contestData = {
      title,
      description,
      startDate,
      endDate,
      requirements,
      prizes,
      type: contestType,
      channelRequirement: {
        required: channelRequired,
        channelUsername: channelRequired ? channelUsername : '',
      },
      status: 'DRAFT',
    };

    if (contestType === 'SPORTS_PREDICTION') {
      // Get questions for sports prediction
      const minCorrectAnswers = parseInt(document.getElementById('minCorrectAnswers').value);
      const questionCards = document.querySelectorAll('.question-card');
      const questions = [];

      for (const card of questionCards) {
        const questionText = card.querySelector('.question-text').value;
        const optionGroups = card.querySelectorAll('.option-group');

        if (optionGroups.length < 2) {
          throw new Error(i18next.t('app.contestForm.minOptionsError'));
        }

        const options = [];

        for (const group of optionGroups) {
          const optionText = group.querySelector('.option-text').value;
          const optionValue = String.fromCharCode(65 + parseInt(group.dataset.index));

          options.push({
            text: optionText,
            value: optionValue,
          });
        }

        questions.push({
          text: questionText,
          options,
        });
      }

      if (questions.length === 0) {
        throw new Error(i18next.t('app.contestForm.minQuestionsError'));
      }

      contestData.questions = questions;
      contestData.minCorrectAnswers = minCorrectAnswers;
    } else if (contestType === 'EMOJI_GAME') {
      // Get emoji game settings
      const gameType = document.getElementById('gameType').value;
      const attemptCount = parseInt(document.getElementById('attemptCount').value);

      if (!gameType) {
        throw new Error('Emoji yarışması için oyun tipi seçmelisiniz');
      }

      const emojiGame = {
        gameType,
        attemptCount,
      };

      // Get game-specific settings
      switch (gameType) {
        case 'DICE':
          const diceTargetType = document.getElementById('diceTargetType').value;
          emojiGame.diceSettings = {
            targetType: diceTargetType,
          };

          if (diceTargetType === 'TOTAL_VALUE') {
            emojiGame.diceSettings.targetValue = parseInt(document.getElementById('targetValue').value);
          } else if (diceTargetType === 'SPECIFIC_VALUE_COUNT') {
            emojiGame.diceSettings.targetValue = parseInt(document.getElementById('specificValue').value);
            emojiGame.diceSettings.targetCount = parseInt(document.getElementById('targetCount').value);
          }
          break;
        case 'BASKETBALL':
        case 'FOOTBALL':
          emojiGame.successTarget = parseInt(document.getElementById('successTarget').value);
          break;
        case 'DART':
          emojiGame.bullseyeTarget = parseInt(document.getElementById('bullseyeTarget').value);
          break;
        case 'BOWLING':
          emojiGame.strikeTarget = parseInt(document.getElementById('strikeTarget').value);
          break;
        case 'SLOT':
          const selectedCombinations = [];
          const checkboxes = document.querySelectorAll('#slotCombinations input[type="checkbox"]:checked');

          if (checkboxes.length === 0) {
            throw new Error('Slot oyunu için en az bir kazanan kombinasyon seçmelisiniz');
          }

          checkboxes.forEach(checkbox => {
            const combination = checkbox.value.split('');
            const name = checkbox.nextElementSibling.textContent.split(' - ')[1];
            selectedCombinations.push({
              combination,
              name,
            });
          });

          emojiGame.slotSettings = {
            winningCombinations: selectedCombinations,
          };
          break;
      }

      contestData.emojiGame = emojiGame;
    }

    // Send request
    await axios.post(`${API_BASE_URL}/contests`, contestData);

    // Redirect to contests page
    loadContests();
  } catch (error) {
    console.error('Create contest error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.contestForm.createError');
    errorElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  }
}

// View contest submissions
async function viewSubmissions(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get contest submissions
    const submissionsResponse = await axios.get(`${API_BASE_URL}/contests/${contestId}/submissions`);
    const { submissions } = submissionsResponse.data;

    // Get contest details
    const contestResponse = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = contestResponse.data;

    // Render submissions
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.submissions.title">Yarışma Katılımları</h1>
        <div>
          <button class="btn btn-secondary me-2" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
          <button onclick="exportToExcel('${contestId}')" class="btn btn-success me-2" data-i18n="app.contestDetail.export">Excel'e Aktar</button>
          ${contest.status === 'COMPLETED' ? `
            <button class="btn btn-primary" onclick="sendNotificationToWinners('${contestId}')" data-i18n="app.contestDetail.notifyWinners">Kazananlara Bildirim Gönder</button>
          ` : ''}
        </div>
      </div>

      <div class="alert alert-info mb-4">
        <h5>${contest.title}</h5>
        <p class="mb-0">
          <span data-i18n="app.submissions.totalParticipants">Toplam Katılım</span>: ${submissions.length}
          ${contest.status === 'COMPLETED' ? `
            | <span data-i18n="app.submissions.winners">Kazananlar</span>: ${submissions.filter(s => s.isWinner).length}
          ` : ''}
        </p>
      </div>

      ${submissions.length > 0 ? `
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th data-i18n="app.submissions.username">Kullanıcı Adı</th>
                <th data-i18n="app.submissions.telegramId">Telegram ID</th>
                <th data-i18n="app.submissions.name">İsim</th>
                ${contest.type === 'EMOJI_GAME' ? `
                  <th data-i18n="app.submissions.gameType">Oyun Tipi</th>
                  <th data-i18n="app.submissions.attempts">Denemeler</th>
                  <th data-i18n="app.submissions.score">Skor</th>
                  <th data-i18n="app.submissions.targetAchieved">Hedef Tutturuldu</th>
                ` : `
                  <th data-i18n="app.submissions.correctAnswers">Doğru Cevaplar</th>
                `}
                <th data-i18n="app.submissions.winner">Kazanan</th>
                <th data-i18n="app.submissions.submissionDate">Gönderim Tarihi</th>
                <th data-i18n="app.submissions.actions">İşlemler</th>
              </tr>
            </thead>
            <tbody>
              ${submissions.map(submission => `
                <tr>
                  <td>${submission.user.username}</td>
                  <td>${submission.user.telegramId}</td>
                  <td>${submission.user.firstName || ''} ${submission.user.lastName || ''}</td>
                  ${contest.type === 'EMOJI_GAME' ? `
                    <td>
                      ${submission.emojiResults ? (() => {
          const gameTypeNames = {
            'DICE': '🎲 Zar',
            'BASKETBALL': '🏀 Basketbol',
            'FOOTBALL': '⚽ Futbol',
            'DART': '🎯 Dart',
            'BOWLING': '🎳 Bowling',
            'SLOT': '🎰 Slot'
          };
          return gameTypeNames[submission.emojiResults.gameType] || submission.emojiResults.gameType;
        })() : '-'}
                    </td>
                    <td>${submission.emojiResults ? submission.emojiResults.attempts.length : 0}</td>
                    <td>${submission.emojiResults ? submission.emojiResults.totalScore : 0}</td>
                    <td>
                      ${submission.emojiResults && submission.emojiResults.achievedTarget
          ? `<span class="badge bg-success">${i18next.t('app.common.yes')}</span>`
          : `<span class="badge bg-danger">${i18next.t('app.common.no')}</span>`}
                    </td>
                  ` : `
                    <td>${submission.correctAnswers !== undefined ? submission.correctAnswers : '-'}</td>
                  `}
                  <td>${submission.isWinner ? `<span class="badge bg-success">${i18next.t('app.common.yes')}</span>` : `<span class="badge bg-secondary">${i18next.t('app.common.no')}</span>`}</td>
                  <td>${new Date(submission.submittedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                  <td>
                    <a href="#" onclick="viewUser('${submission.user._id}')" class="btn btn-sm btn-primary" data-i18n="app.submissions.viewUser">Kullanıcıyı Görüntüle</a>
                    ${contest.type === 'EMOJI_GAME' ? `
                      <button class="btn btn-sm btn-info" onclick="viewEmojiGameDetails('${submission._id}')" data-i18n="app.submissions.viewGameDetails">Oyun Detayları</button>
                    ` : `
                      <button class="btn btn-sm btn-info" onclick="viewSubmissionAnswers('${submission._id}')" data-i18n="app.submissions.viewAnswers">Cevapları Görüntüle</button>
                    `}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      ` : `
        <div class="alert alert-warning">
          <span data-i18n="app.submissions.noSubmissions">Bu yarışmaya henüz katılım yok.</span>
        </div>
      `}
    `;

    // Update translations
    updateUI();
  } catch (error) {
    console.error('Submissions error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        <span data-i18n="app.submissions.error">Katılımlar yüklenirken bir hata oluştu. Lütfen tekrar deneyin.</span>
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// Helper function to get badge class based on contest status
function getBadgeClass(status) {
  switch (status) {
    case 'ACTIVE':
      return 'bg-success';
    case 'DRAFT':
      return 'bg-secondary';
    case 'COMPLETED':
      return 'bg-primary';
    case 'CANCELLED':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
}

// Edit contest
async function editContest(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get contest details
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = response.data;

    // Format dates for datetime-local input with timezone adjustment
    const startDateObj = new Date(contest.startDate);
    const endDateObj = new Date(contest.endDate);

    // Adjust for timezone offset
    const startDate = new Date(startDateObj.getTime() - startDateObj.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    const endDate = new Date(endDateObj.getTime() - endDateObj.getTimezoneOffset() * 60000).toISOString().slice(0, 16);

    // Render edit form
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.contestForm.editTitle">Yarışma Düzenle</h1>
        <button class="btn btn-secondary" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
      </div>

      <form id="edit-contest-form">
        <div class="card mb-4">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0" data-i18n="app.contestForm.contestInfo">Yarışma Bilgileri</h5>
            <span class="badge ${getBadgeClass(contest.status)}">${contest.status}</span>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label for="title" class="form-label" data-i18n="app.contestForm.title">Başlık</label>
              <input type="text" class="form-control" id="title" name="title" value="${contest.title}" required>
            </div>

            <div class="mb-3">
              <label for="description" class="form-label" data-i18n="app.contestForm.description">Açıklama</label>
              <textarea class="form-control" id="description" name="description" rows="3" required>${contest.description}</textarea>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="startDate" class="form-label" data-i18n="app.contestForm.startDate">Başlangıç Tarihi</label>
                  <input type="datetime-local" class="form-control" id="startDate" name="startDate" value="${startDate}" required>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="endDate" class="form-label" data-i18n="app.contestForm.endDate">Bitiş Tarihi</label>
                  <input type="datetime-local" class="form-control" id="endDate" name="endDate" value="${endDate}" required>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="requirements" class="form-label"><span data-i18n="app.contestForm.requirements">Katılım Şartları</span> <span data-i18n="app.contestForm.optional">(İsteğe Bağlı)</span></label>
              <textarea class="form-control" id="requirements" name="requirements" rows="2">${contest.requirements || ''}</textarea>
            </div>

            <div class="mb-3">
              <label for="prizes" class="form-label"><span data-i18n="app.contestForm.prizes">Ödüller</span> <span data-i18n="app.contestForm.optional">(İsteğe Bağlı)</span></label>
              <textarea class="form-control" id="prizes" name="prizes" rows="2">${contest.prizes || ''}</textarea>
            </div>

            ${contest.type === 'SPORTS_PREDICTION' ? `
            <div class="mb-3" id="minCorrectAnswersContainer">
              <label for="minCorrectAnswers" class="form-label" data-i18n="app.contestDetail.minCorrectAnswers">Kazanmak için Gereken Minimum Doğru Cevap Sayısı</label>
              <input type="number" class="form-control" id="minCorrectAnswers" name="minCorrectAnswers" min="1" value="${contest.minCorrectAnswers}" required>
            </div>
            ` : ''}

            <div class="form-check form-switch mb-3">
              <input class="form-check-input" type="checkbox" id="channelRequired" name="channelRequired" ${contest.channelRequirement.required ? 'checked' : ''}>
              <label class="form-check-label" for="channelRequired" data-i18n="app.contestForm.requireChannel">Kanal Üyeliği Zorunlu</label>
            </div>

            <div class="mb-3" id="channelUsernameContainer" style="display: ${contest.channelRequirement.required ? 'block' : 'none'};">
              <label for="channelUsername" class="form-label" data-i18n="app.contestForm.channelUsername">Kanal Kullanıcı Adı</label>
              <input type="text" class="form-control" id="channelUsername" name="channelUsername" placeholder="@kanaladi" value="${contest.channelRequirement.channelUsername || ''}">
            </div>

            ${contest.status === 'DRAFT' ? `
              <div class="mb-3">
                <label for="status" class="form-label" data-i18n="app.contestDetail.status">Durum</label>
                <select class="form-select" id="status" name="status">
                  <option value="DRAFT" ${contest.status === 'DRAFT' ? 'selected' : ''} data-i18n="app.common.draft">Taslak</option>
                  <option value="ACTIVE" ${contest.status === 'ACTIVE' ? 'selected' : ''} data-i18n="app.common.active">Aktif</option>
                </select>
              </div>
            ` : ''}
          </div>
        </div>

        ${contest.type === 'SPORTS_PREDICTION' ? `
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0" data-i18n="app.contestDetail.questions">Sorular</h5>
          </div>
          <div class="card-body">
            <div id="questionsContainer">
              ${contest.questions.map((question, qIndex) => `
                <div class="card mb-3 question-card" data-index="${qIndex}">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><span data-i18n="app.contestDetail.question">Soru</span> ${qIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-question-btn" data-i18n="app.contestForm.removeQuestion">Kaldır</button>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label class="form-label" data-i18n="app.contestForm.questionText">Soru Metni</label>
                      <input type="text" class="form-control question-text" name="questionText" value="${question.text}" required>
                    </div>

                    <div class="options-container mb-3">
                      ${question.options.map((option, oIndex) => `
                        <div class="input-group mb-2 option-group" data-index="${oIndex}">
                          <span class="input-group-text"><span data-i18n="app.contestForm.option">Seçenek</span> ${String.fromCharCode(65 + oIndex)}</span>
                          <input type="text" class="form-control option-text" name="optionText" value="${option.text}" required>
                          <button type="button" class="btn btn-outline-danger remove-option-btn" data-i18n="app.contestForm.removeOption">Kaldır</button>
                        </div>
                      `).join('')}
                    </div>

                    <button type="button" class="btn btn-sm btn-success add-option-btn" data-i18n="app.contestForm.addOption">Seçenek Ekle</button>
                  </div>
                </div>
              `).join('')}
            </div>

            <button type="button" class="btn btn-primary mt-3" id="addQuestionBtn" data-i18n="app.contestForm.addQuestion">Soru Ekle</button>
          </div>
        </div>
        ` : ''}

        <div id="edit-contest-error" class="alert alert-danger d-none"></div>

        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary" data-i18n="app.contestForm.saveChanges">Değişiklikleri Kaydet</button>
          <button type="button" class="btn btn-secondary" onclick="viewContest('${contestId}')" data-i18n="app.contestForm.cancel">İptal</button>
        </div>
      </form>
    `;

    // Add event listeners
    document.getElementById('channelRequired').addEventListener('change', function () {
      document.getElementById('channelUsernameContainer').style.display = this.checked ? 'block' : 'none';
    });

    // Add question button event listener (only for sports prediction contests)
    const addQuestionBtn = document.getElementById('addQuestionBtn');
    if (addQuestionBtn) {
      addQuestionBtn.addEventListener('click', addQuestion);
    }

    // Add event listeners to existing questions and options
    document.querySelectorAll('.remove-question-btn').forEach(btn => {
      btn.addEventListener('click', function () {
        const questionCard = this.closest('.question-card');
        questionCard.remove();
        updateQuestionNumbers();
      });
    });

    document.querySelectorAll('.add-option-btn').forEach(btn => {
      btn.addEventListener('click', function () {
        const questionCard = this.closest('.question-card');
        addOption(questionCard);
      });
    });

    document.querySelectorAll('.remove-option-btn').forEach(btn => {
      btn.addEventListener('click', function () {
        const optionGroup = this.closest('.option-group');
        const questionCard = this.closest('.question-card');
        optionGroup.remove();
        updateOptionLetters(questionCard);
      });
    });

    // Add submit event listener
    document.getElementById('edit-contest-form').addEventListener('submit', function (e) {
      e.preventDefault();
      handleEditContest(e, contestId);
    });
  } catch (error) {
    console.error('Edit contest error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        Yarışma düzenleme formu yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Handle edit contest form submission
async function handleEditContest(e, contestId) {
  e.preventDefault();

  const errorElement = document.getElementById('edit-contest-error');
  errorElement.classList.add('d-none');

  try {
    // Validate dates
    const startDateObj = new Date(document.getElementById('startDate').value);
    const endDateObj = new Date(document.getElementById('endDate').value);

    if (endDateObj <= startDateObj) {
      throw new Error(i18next.t('app.contestForm.dateError'));
    }
    // Get form data
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const requirements = document.getElementById('requirements').value;
    const prizes = document.getElementById('prizes').value;
    const channelRequired = document.getElementById('channelRequired').checked;
    const channelUsername = document.getElementById('channelUsername').value;
    const statusElement = document.getElementById('status');
    const status = statusElement ? statusElement.value : undefined;

    // Get questions (only for sports prediction contests)
    let questions = [];
    if (document.getElementById('questionsContainer')) {
      const questionCards = document.querySelectorAll('.question-card');

      for (const card of questionCards) {
        const questionText = card.querySelector('.question-text').value;
        const optionGroups = card.querySelectorAll('.option-group');

        if (optionGroups.length < 2) {
          throw new Error(i18next.t('app.contestForm.minOptionsError'));
        }

        const options = [];

        for (const group of optionGroups) {
          const optionText = group.querySelector('.option-text').value;
          const optionValue = String.fromCharCode(65 + parseInt(group.dataset.index));

          options.push({
            text: optionText,
            value: optionValue,
          });
        }

        questions.push({
          text: questionText,
          options,
        });
      }

      if (questions.length === 0) {
        throw new Error(i18next.t('app.contestForm.minQuestionsError'));
      }
    }

    // Create contest data
    const contestData = {
      title,
      description,
      startDate,
      endDate,
      requirements,
      prizes,
      channelRequirement: {
        required: channelRequired,
        channelUsername: channelRequired ? channelUsername : '',
      },
    };

    // Add type-specific data (only for sports prediction contests)
    if (document.getElementById('minCorrectAnswers')) {
      const minCorrectAnswers = parseInt(document.getElementById('minCorrectAnswers').value);
      contestData.minCorrectAnswers = minCorrectAnswers;
      contestData.questions = questions;
    }

    // Add status if available
    if (status) {
      contestData.status = status;
    }

    // Send request
    await axios.put(`${API_BASE_URL}/contests/${contestId}`, contestData);

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Edit contest error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.contestForm.editError');
    errorElement.classList.remove('d-none');

    // Scroll to top
    window.scrollTo(0, 0);
  }
}

// Set correct answers for a contest
async function setCorrectAnswers(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  // Show loading
  app.innerHTML = `
    <div class="text-center">
      <div class="spinner-border" role="status">
        <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
      </div>
    </div>
  `;

  try {
    // Get contest details
    const response = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = response.data;

    // Render correct answers form
    app.innerHTML = `
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 data-i18n="app.contestDetail.setCorrectAnswers">Doğru Cevapları Belirle</h1>
        <button class="btn btn-secondary" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
      </div>

      <div class="alert alert-info mb-4">
        <h5>${contest.title}</h5>
        <p data-i18n="app.contestDetail.setCorrectAnswersInfo">Her soru için doğru cevabı seçin. Bu işlem, yarışma tamamlandıktan sonra kazananları belirlemek için kullanılacaktır.</p>
      </div>

      <form id="correct-answers-form">
        ${contest.questions.map((question, index) => `
          <div class="card mb-3">
            <div class="card-header">
              <h5 class="mb-0"><span data-i18n="app.contestDetail.question">Soru</span> ${index + 1}: ${question.text}</h5>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label class="form-label" data-i18n="app.contestDetail.selectCorrectAnswer">Doğru Cevap:</label>
                <select class="form-select correct-answer" data-question-index="${index}">
                  <option value="" data-i18n="app.contestDetail.select">Seçiniz...</option>
                  ${question.options.map(option => `
                    <option value="${option.value}" ${question.correctAnswer === option.value ? 'selected' : ''}>
                      ${option.text} (${option.value})
                    </option>
                  `).join('')}
                </select>
              </div>
            </div>
          </div>
        `).join('')}

        <div id="correct-answers-error" class="alert alert-danger d-none"></div>

        <div class="d-flex gap-2">
          <button type="submit" class="btn btn-primary" data-i18n="app.contestDetail.saveCorrectAnswers">Doğru Cevapları Kaydet</button>
          <button type="button" class="btn btn-success" onclick="saveAnswersAndCompleteContest('${contestId}')" data-i18n="app.contestDetail.saveAndComplete">Doğru Cevapları Kaydet ve Yarışmayı Tamamla</button>
          <button type="button" class="btn btn-secondary" onclick="viewContest('${contestId}')" data-i18n="app.contestForm.cancel">İptal</button>
        </div>
      </form>
    `;

    // Add submit event listener
    document.getElementById('correct-answers-form').addEventListener('submit', function (e) {
      e.preventDefault();
      handleSetCorrectAnswers(e, contestId, contest.questions.length);
    });
  } catch (error) {
    console.error('Set correct answers error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.contestDetail.correctAnswersError')}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Handle set correct answers form submission
async function handleSetCorrectAnswers(e, contestId, questionCount) {
  e.preventDefault();

  const errorElement = document.getElementById('correct-answers-error');
  errorElement.classList.add('d-none');

  try {
    // Get correct answers
    const correctAnswers = [];
    let hasEmptyAnswer = false;

    for (let i = 0; i < questionCount; i++) {
      const select = document.querySelector(`.correct-answer[data-question-index="${i}"]`);
      const value = select.value;

      if (!value) {
        hasEmptyAnswer = true;
      }

      correctAnswers.push(value);
    }

    if (hasEmptyAnswer) {
      throw new Error(i18next.t('app.contestDetail.correctAnswersError'));
    }

    // Send request
    await axios.put(`${API_BASE_URL}/contests/${contestId}/set-answers`, { correctAnswers });

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Set correct answers error:', error);
    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.contestDetail.correctAnswersError');
    errorElement.classList.remove('d-none');
  }
}

// Save answers and complete contest
async function saveAnswersAndCompleteContest(contestId) {
  // Önce kullanıcıdan onay al
  if (!confirm(i18next.t('app.contestDetail.saveAndCompleteConfirm') || 'Doğru cevapları kaydedip yarışmayı tamamlamak istediğinize emin misiniz? Bu işlem geri alınamaz.')) {
    return;
  }

  const errorElement = document.getElementById('correct-answers-error');
  errorElement.classList.add('d-none');

  try {
    // Get correct answers
    const questionCount = document.querySelectorAll('.correct-answer').length;
    const correctAnswers = [];
    let hasEmptyAnswer = false;

    for (let i = 0; i < questionCount; i++) {
      const select = document.querySelector(`.correct-answer[data-question-index="${i}"]`);
      const value = select.value;

      if (!value) {
        hasEmptyAnswer = true;
      }

      correctAnswers.push(value);
    }

    if (hasEmptyAnswer) {
      throw new Error(i18next.t('app.contestDetail.correctAnswersError') || 'Lütfen tüm sorular için doğru cevapları belirleyin.');
    }

    // Show loading
    app.innerHTML = `
      <div class="text-center">
        <div class="spinner-border" role="status">
          <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
        </div>
        <p class="mt-3" data-i18n="app.contestDetail.savingAndCompleting">Doğru cevaplar kaydediliyor ve yarışma tamamlanıyor...</p>
      </div>
    `;

    // Update translations
    updateUI();

    // 1. Önce doğru cevapları kaydet
    await axios.put(`${API_BASE_URL}/contests/${contestId}/set-answers`, { correctAnswers });

    // 2. Sonra yarışmayı tamamla
    await axios.put(`${API_BASE_URL}/contests/${contestId}/complete`);

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Save answers and complete contest error:', error);

    // Hata durumunda kullanıcıya bilgi ver
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.contestDetail.saveAndCompleteError') || 'Doğru cevaplar kaydedilirken veya yarışma tamamlanırken bir hata oluştu:'}
        ${error.response?.data?.message || error.message || ''}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="setCorrectAnswers('${contestId}')" data-i18n="app.contestDetail.returnToAnswers">Doğru Cevapları Belirle Sayfasına Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// Activate contest
async function activateContest(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  if (!confirm(i18next.t('app.contestDetail.activateConfirm'))) {
    return;
  }

  try {
    // Show loading
    app.innerHTML = `
      <div class="text-center">
        <div class="spinner-border" role="status">
          <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
        </div>
        <p class="mt-3" data-i18n="app.contestDetail.activating">Yarışma aktifleştiriliyor...</p>
      </div>
    `;

    // Send request
    await axios.put(`${API_BASE_URL}/contests/${contestId}/activate`);

    // Redirect to contest details
    viewContest(contestId);
  } catch (error) {
    console.error('Activate contest error:', error);
    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.contestDetail.activateError')}: ${error.response?.data?.message || error.message || i18next.t('app.common.unknown')}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
      </div>
    `;
  }
}

// Complete contest
async function completeContest(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  if (!confirm(i18next.t('app.contestDetail.completeConfirm'))) {
    return;
  }

  try {
    // Show loading
    app.innerHTML = `
      <div class="text-center">
        <div class="spinner-border" role="status">
          <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
        </div>
        <p class="mt-3" data-i18n="app.contestDetail.completing">Yarışma tamamlanıyor ve kazananlar hesaplanıyor...</p>
      </div>
    `;

    try {
      // Önce yarışma detaylarını al ve doğru cevapların girilip girilmediğini kontrol et
      const contestResponse = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
      const { contest } = contestResponse.data;

      // Tüm soruların doğru cevapları girilmiş mi kontrol et
      const missingAnswers = contest.questions.some(question => !question.correctAnswer);

      if (missingAnswers) {
        // Eğer eksik cevaplar varsa, doğru cevapları belirle sayfasına yönlendir
        showToast(i18next.t('app.contestDetail.correctAnswersRequired') || 'Yarışmayı tamamlamadan önce tüm soruların doğru cevaplarını belirlemelisiniz.', 'warning');

        // Kısa bir gecikme ekle
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Doğru cevapları belirle sayfasına yönlendir
        setCorrectAnswers(contestId);
        return;
      }

      // Doğru cevaplar girilmişse, yarışmayı tamamla
      await axios.put(`${API_BASE_URL}/contests/${contestId}/complete`);

      // Redirect to contest details
      viewContest(contestId);
    } catch (error) {
      throw error; // Hata durumunda catch bloğuna yönlendir
    }
  } catch (error) {
    console.error('Complete contest error:', error);

    // Hata mesajını göster
    let errorMessage = 'Bilinmeyen bir hata';

    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    app.innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.contestDetail.completeError') || 'Yarışma tamamlanırken bir hata oluştu:'} ${errorMessage}
      </div>
      <div class="text-center mt-3">
        <button class="btn btn-primary" onclick="viewContest('${contestId}')" data-i18n="app.contestDetail.returnToDetails">Yarışma Detaylarına Dön</button>
      </div>
    `;

    // Update translations
    updateUI();
  }
}

// Show cancel contest modal
function showCancelContestModal(contestId) {
  // Create modal if it doesn't exist
  if (!document.getElementById('cancelContestModal')) {
    const modalHTML = `
      <div class="modal fade" id="cancelContestModal" tabindex="-1" aria-labelledby="cancelContestModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="cancelContestModalLabel" data-i18n="app.contests.cancelTitle">Yarışmayı İptal Et</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <p data-i18n="app.contests.cancelConfirmText">Bu yarışmayı iptal etmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
              <form id="cancelContestForm">
                <input type="hidden" id="cancelContestId" value="">
                <div class="mb-3">
                  <label for="cancelReason" class="form-label" data-i18n="app.contests.cancelReason">İptal Nedeni</label>
                  <textarea class="form-control" id="cancelReason" rows="3"></textarea>
                  <div class="form-text" data-i18n="app.contests.cancelReasonHelp">Yarışmanın neden iptal edildiğini açıklayın (isteğe bağlı)</div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-i18n="app.common.cancel">İptal</button>
              <button type="button" class="btn btn-danger" onclick="cancelContest()" data-i18n="app.contests.confirmCancel">Yarışmayı İptal Et</button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    updateUI();
  }

  // Set contest ID in the form
  document.getElementById('cancelContestId').value = contestId;
  document.getElementById('cancelReason').value = '';

  // Show modal
  const cancelModal = new bootstrap.Modal(document.getElementById('cancelContestModal'));
  cancelModal.show();
}

// Cancel contest
async function cancelContest() {
  try {
    const contestId = document.getElementById('cancelContestId').value;
    const reason = document.getElementById('cancelReason').value;

    // Close modal
    const cancelModal = bootstrap.Modal.getInstance(document.getElementById('cancelContestModal'));
    cancelModal.hide();

    // Show loading
    showToast(i18next.t('app.common.loading'), 'info');

    // Call API to cancel contest
    const response = await axios.put(`${API_BASE_URL}/contests/${contestId}/cancel`, { reason });

    if (response.data.success) {
      // Show success message
      showToast(i18next.t('app.contests.cancelSuccess'), 'success');

      // Reload current page
      if (window.state.currentPage === 'contests') {
        loadContests();
      } else {
        viewContest(contestId);
      }
    }
  } catch (error) {
    console.error('Cancel contest error:', error);
    showToast(i18next.t('app.contests.cancelError'), 'danger');
  }
}

// Export contest submissions to Excel
async function exportToExcel(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  try {
    // Get the token from state
    const token = state.token;

    // Open in a new window with the token in the URL
    window.open(`${API_BASE_URL}/contests/${contestId}/export?token=${token}`, '_blank');
  } catch (error) {
    console.error('Export error:', error);
    alert('Excel\'e aktarma işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.');
  }
}

// View submission answers
async function viewSubmissionAnswers(submissionId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  try {
    // Show loading in modal
    const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
    document.getElementById('dynamicModalTitle').innerText = i18next.t('app.submissions.viewAnswersTitle');
    document.getElementById('dynamicModalBody').innerHTML = `
      <div class="text-center">
        <div class="spinner-border" role="status">
          <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
        </div>
      </div>
    `;
    document.getElementById('dynamicModalFooter').innerHTML = `
      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-i18n="app.common.close">Kapat</button>
    `;
    modal.show();

    // Get submission details
    const response = await axios.get(`${API_BASE_URL}/contests/submissions/${submissionId}`);
    const { submission } = response.data;

    // Update modal content
    document.getElementById('dynamicModalTitle').innerText = i18next.t('app.submissions.answersFor', { username: submission.user.username });

    // Create content for modal body
    let content = `
      <div class="mb-3">
        <strong data-i18n="app.submissions.contest">Yarışma:</strong> ${submission.contest.title}
        <br>
        <strong data-i18n="app.submissions.user">Kullanıcı:</strong> ${submission.user.firstName || ''} ${submission.user.lastName || ''} (@${submission.user.username})
        <br>
        <strong data-i18n="app.submissions.correctAnswers">Doğru Cevaplar:</strong> ${submission.correctAnswers}/${submission.detailedAnswers.length}
        <br>
        <strong data-i18n="app.submissions.submissionDate">Gönderim Tarihi:</strong> ${new Date(submission.submittedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}
      </div>

      <div class="accordion" id="answersAccordion">
    `;

    // Add each question and answer to the accordion
    submission.detailedAnswers.forEach((answer, index) => {
      content += `
        <div class="accordion-item">
          <h2 class="accordion-header" id="heading${index}">
            <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}" aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="collapse${index}">
              <span class="me-2">${answer.questionNumber}.</span>
              ${answer.questionText.substring(0, 50)}${answer.questionText.length > 50 ? '...' : ''}
              ${answer.isCorrect
          ? `<span class="badge bg-success ms-2" data-i18n="app.submissions.correct">Doğru</span>`
          : answer.correctAnswerText == ""
            ? `<span class="badge bg-secondary ms-2" data-i18n="app.submissions.notSet">Belirtilmedi</span>`
            : `<span class="badge bg-danger ms-2" data-i18n="app.submissions.incorrect">Yanlış</span>`}
            </button>
          </h2>
          <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" aria-labelledby="heading${index}" data-bs-parent="#answersAccordion">
            <div class="accordion-body">
              <p><strong data-i18n="app.submissions.question">Soru:</strong> ${answer.questionText}</p>
              <p><strong data-i18n="app.submissions.userAnswer">Kullanıcı Cevabı:</strong> ${answer.userAnswerText || i18next.t('app.submissions.noAnswer')}</p>
              <p><strong data-i18n="app.submissions.correctAnswer">Doğru Cevap:</strong> ${answer.correctAnswerText || i18next.t('app.submissions.notSet')}</p>
            </div>
          </div>
        </div>
      `;
    });

    content += `</div>`;
    document.getElementById('dynamicModalBody').innerHTML = content;

    // Initialize i18next for the modal content
    i18next.reloadResources().then(() => {
      document.getElementById('dynamicModalBody').querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.innerHTML = i18next.t(key);
      });
      document.getElementById('dynamicModalFooter').querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.innerHTML = i18next.t(key);
      });
    });

  } catch (error) {
    console.error('View submission answers error:', error);
    document.getElementById('dynamicModalBody').innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.submissions.answersError')}
      </div>
    `;
  }
}

// View emoji game details
async function viewEmojiGameDetails(submissionId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  try {
    // Show loading in modal
    const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
    document.getElementById('dynamicModalTitle').innerText = i18next.t('app.submissions.gameDetailsTitle');
    document.getElementById('dynamicModalBody').innerHTML = `
      <div class="text-center">
        <div class="spinner-border" role="status">
          <span class="visually-hidden" data-i18n="app.common.loading">Yükleniyor...</span>
        </div>
      </div>
    `;
    document.getElementById('dynamicModalFooter').innerHTML = `
      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-i18n="app.common.close">Kapat</button>
    `;
    modal.show();

    // Get submission details
    const response = await axios.get(`${API_BASE_URL}/contests/submissions/${submissionId}`);
    const { submission } = response.data;

    // Update modal content
    document.getElementById('dynamicModalTitle').innerText = i18next.t('app.submissions.gameDetailsFor', { username: submission.user.username });

    // Get game type display name
    const gameTypeNames = {
      'DICE': '🎲 Zar',
      'BASKETBALL': '🏀 Basketbol',
      'FOOTBALL': '⚽ Futbol',
      'DART': '🎯 Dart',
      'BOWLING': '🎳 Bowling',
      'SLOT': '🎰 Slot'
    };
    const gameTypeName = gameTypeNames[submission.emojiResults?.gameType] || submission.emojiResults?.gameType || 'Bilinmiyor';

    // Create content for modal body
    let content = `
      <div class="mb-3">
        <strong data-i18n="app.submissions.contest">Yarışma:</strong> ${submission.contest.title}
        <br>
        <strong data-i18n="app.submissions.user">Kullanıcı:</strong> ${submission.user.firstName || ''} ${submission.user.lastName || ''} (@${submission.user.username})
        <br>
        <strong data-i18n="app.submissions.gameType">Oyun Tipi:</strong> ${gameTypeName}
        <br>
        <strong data-i18n="app.submissions.totalAttempts">Toplam Deneme:</strong> ${submission.emojiResults?.attempts?.length || 0}
        <br>
        <strong data-i18n="app.submissions.finalScore">Final Skoru:</strong> ${submission.emojiResults?.totalScore || 0}
        <br>
        <strong data-i18n="app.submissions.targetAchieved">Hedef Tutturuldu:</strong>
        ${submission.emojiResults?.achievedTarget
        ? `<span class="badge bg-success">${i18next.t('app.common.yes')}</span>`
        : `<span class="badge bg-danger">${i18next.t('app.common.no')}</span>`}
        <br>
        <strong data-i18n="app.submissions.submissionDate">Gönderim Tarihi:</strong> ${new Date(submission.submittedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { day: 'numeric', month: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}
      </div>
    `;

    // Add attempts details if available
    if (submission.emojiResults?.attempts && submission.emojiResults.attempts.length > 0) {
      content += `
        <div class="mt-4">
          <h6 data-i18n="app.submissions.attemptDetails">Deneme Detayları:</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th data-i18n="app.submissions.attemptNumber">Deneme</th>
                  <th data-i18n="app.submissions.result">Sonuç</th>
                  <th data-i18n="app.submissions.success">Başarılı</th>
                  <th data-i18n="app.submissions.timestamp">Zaman</th>
                </tr>
              </thead>
              <tbody>
      `;

      submission.emojiResults.attempts.forEach((attempt, index) => {
        let resultDisplay = attempt.result;

        // Format result based on game type
        if (submission.emojiResults.gameType === 'DICE') {
          resultDisplay = `🎲 ${attempt.result}`;
        } else if (submission.emojiResults.gameType === 'BASKETBALL') {
          resultDisplay = attempt.success ? '🏀 ✅ Başarılı' : '🏀 ❌ Kaçırdı';
        } else if (submission.emojiResults.gameType === 'FOOTBALL') {
          resultDisplay = attempt.success ? '⚽ ⚽ Gol!' : '⚽ ❌ Kaçırdı';
        } else if (submission.emojiResults.gameType === 'DART') {
          resultDisplay = attempt.success ? '🎯 🎯 Bullseye!' : `🎯 ${attempt.result} puan`;
        } else if (submission.emojiResults.gameType === 'BOWLING') {
          resultDisplay = attempt.success ? '🎳 🎳 Strike!' : '🎳 ❌ Kaçırdı';
        } else if (submission.emojiResults.gameType === 'SLOT') {
          resultDisplay = `🎰 ${attempt.result}`;
        }

        content += `
          <tr>
            <td>${index + 1}</td>
            <td>${resultDisplay}</td>
            <td>
              ${attempt.success
            ? `<span class="badge bg-success">✅</span>`
            : `<span class="badge bg-danger">❌</span>`}
            </td>
            <td>${new Date(attempt.timestamp).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : i18next.language === 'de' ? 'de-DE' : i18next.language === 'ar' ? 'ar-SA' : 'en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit' })}</td>
          </tr>
        `;
      });

      content += `
              </tbody>
            </table>
          </div>
        </div>
      `;
    }

    document.getElementById('dynamicModalBody').innerHTML = content;

    // Initialize i18next for the modal content
    i18next.reloadResources().then(() => {
      document.getElementById('dynamicModalBody').querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.innerHTML = i18next.t(key);
      });
      document.getElementById('dynamicModalFooter').querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.innerHTML = i18next.t(key);
      });
    });

  } catch (error) {
    console.error('View emoji game details error:', error);
    document.getElementById('dynamicModalBody').innerHTML = `
      <div class="alert alert-danger">
        ${i18next.t('app.submissions.gameDetailsError')}
      </div>
    `;
  }
}

// Send notification to contest winners
async function sendNotificationToWinners(contestId) {
  if (!state.isAuthenticated) {
    return loadLoginPage();
  }

  try {
    // Get contest details
    const contestResponse = await axios.get(`${API_BASE_URL}/contests/${contestId}`);
    const { contest } = contestResponse.data;

    // Get winners
    const winnersResponse = await axios.get(`${API_BASE_URL}/contests/${contestId}/winners`);
    const { winners } = winnersResponse.data;

    if (winners.length === 0) {
      alert(i18next.t('app.contestDetail.noWinners') || 'Bu yarışmada kazanan bulunamadı.');
      return;
    }

    // Prepare notification data
    const title = `${contest.title} - Kazananlar Bildirimi`;
    const message = `Tebrikler! "${contest.title}" yarışmasını kazandınız. ${contest.prizes ? `\n\nÖdül bilgisi: ${contest.prizes}` : ''}`;

    // Kullanıcıya bilgi ver
    alert(i18next.t('app.contestDetail.notifyWinnersInfo') || 'Bildirim gönderme işlemi otomatik olarak başlatılacaktır');

    // Önce modals div'ini kontrol et ve gerekirse oluştur
    let notificationModals = document.getElementById('notificationModals');
    if (!notificationModals) {
      notificationModals = document.createElement('div');
      notificationModals.id = 'notificationModals';
      document.body.appendChild(notificationModals);
    }

    // Notification modals içeriğini yükle
    try {
      const response = await axios.get('/modals/notification-modal.html');
      notificationModals.innerHTML = response.data;

      // Modalları yükledikten sonra işlemlere devam et
      // Open notification modal
      const modal = document.getElementById('createNotificationModal');

      if (!modal) {
        throw new Error('createNotificationModal elementi bulunamadı');
      }

      // Modal'ı güvenli bir şekilde oluştur
      try {
        // Önce mevcut bir instance var mı kontrol et
        let modalInstance = bootstrap.Modal.getInstance(modal);

        // Eğer yoksa yeni bir instance oluştur
        if (!modalInstance) {
          modalInstance = new bootstrap.Modal(modal, {
            backdrop: true,
            keyboard: true,
            focus: true
          });
        }

        // Modal'ı göster
        modalInstance.show();

        // Modal tamamen yüklendikten sonra form elemanlarını doldur
        setTimeout(() => {
          // Set notification data
          const titleInput = document.getElementById('notificationTitle');
          const messageInput = document.getElementById('notificationMessage');

          if (titleInput) titleInput.value = title;
          if (messageInput) messageInput.value = message;

          // Önizleme seçeneğini her zaman seçili yap
          const previewCheckbox = document.getElementById('previewBeforeSend');
          if (previewCheckbox) {
            previewCheckbox.checked = true;
          }
        }, 500); // Modal açılması için biraz bekle
      } catch (error) {
        console.error('Modal oluşturma hatası:', error);
        alert('Bildirim oluşturma modalı açılamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
      }

      // Store winners in a global variable to use when sending
      window.contestWinners = winners.map(winner => winner.user._id);

      // Kazananlara bildirim gönderme işlemi olduğunu belirt
      // Global değişken yerine veri özniteliği kullan
      document.body.setAttribute('data-winner-notification', 'true');

    } catch (error) {
      console.error('Modal yükleme hatası:', error);
      alert('Bildirim modalları yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
    }

    // Add event listener to override the default notification creation
    setTimeout(() => {
      try {
        const createBtn = document.getElementById('createNotificationBtn');
        if (createBtn) {
          const originalClickHandler = createBtn.onclick;

          createBtn.onclick = async () => {
            const title = document.getElementById('notificationTitle').value;
            const message = document.getElementById('notificationMessage').value;
            const errorElement = document.getElementById('notificationError');

            // Form doğrulama
            errorElement.classList.add('d-none');

            // Başlık ve mesaj kontrolü
            if (!title.trim()) {
              errorElement.textContent = i18next.t('app.notifications.titleRequired') || 'Başlık alanı zorunludur';
              errorElement.classList.remove('d-none');
              return;
            }

            if (!message.trim()) {
              errorElement.textContent = i18next.t('app.notifications.messageRequired') || 'Mesaj alanı zorunludur';
              errorElement.classList.remove('d-none');
              return;
            }

            // Get URLs
            const urls = [];
            document.querySelectorAll('.notification-url-input').forEach(input => {
              if (input.value.trim()) {
                urls.push(input.value.trim());
              }
            });

            // Get buttons
            const buttons = [];
            let invalidButtonUrl = false;
            let invalidButtonText = false;

            document.querySelectorAll('.notification-button-group').forEach((group) => {
              const textInput = group.querySelector('.notification-button-text');
              const urlInput = group.querySelector('.notification-button-url');

              // Buton metni kontrolü
              if (textInput && !textInput.value.trim() && urlInput && urlInput.value.trim()) {
                invalidButtonText = true;
                return;
              }

              // Buton URL kontrolü
              if (urlInput && textInput && textInput.value.trim() && !urlInput.value.trim()) {
                invalidButtonUrl = true;
                return;
              }

              // URL formatı kontrolü
              if (urlInput && urlInput.value.trim()) {
                try {
                  new URL(urlInput.value.trim());
                } catch (e) {
                  invalidButtonUrl = true;
                  return;
                }
              }

              if (textInput && urlInput && textInput.value.trim() && urlInput.value.trim()) {
                buttons.push({
                  text: textInput.value.trim(),
                  url: urlInput.value.trim()
                });
              }
            });

            // Buton doğrulama hataları
            if (invalidButtonText) {
              errorElement.textContent = i18next.t('app.notifications.buttonTextRequired') || 'Buton metni alanı zorunludur';
              errorElement.classList.remove('d-none');
              return;
            }

            if (invalidButtonUrl) {
              errorElement.textContent = i18next.t('app.notifications.buttonUrlInvalid') || 'Buton URL alanı geçerli bir URL olmalıdır';
              errorElement.classList.remove('d-none');
              return;
            }

            // Get image if exists
            const imageInput = document.getElementById('notificationImage');
            const formData = new FormData();
            formData.append('title', title);
            formData.append('message', message);

            // Add recipients (winners)
            if (window.contestWinners && window.contestWinners.length > 0) {
              window.contestWinners.forEach((winnerId, index) => {
                formData.append(`recipients[${index}]`, winnerId);
              });
            }

            // Önemli: urls'i string olarak değil, her bir URL'i ayrı ayrı ekle
            if (urls.length > 0) {
              urls.forEach((url, index) => {
                formData.append(`urls[${index}]`, url);
              });
            }

            // Butonları ekle
            if (buttons.length > 0) {
              formData.append('buttons', JSON.stringify(buttons));
            }

            if (imageInput && imageInput.files.length > 0) {
              formData.append('image', imageInput.files[0]);
            }

            try {
              // Check if preview is required
              if (document.getElementById('previewBeforeSend').checked) {
                // Show preview
                const previewModal = document.getElementById('previewNotificationModal');
                const previewMessageContent = document.getElementById('previewMessageContent');
                const previewImageContainer = document.getElementById('previewImageContainer');
                const previewImage = document.getElementById('previewImage');

                // Set preview content
                previewMessageContent.innerHTML = message.replace(/\n/g, '<br>');

                // Show/hide image preview
                if (imageInput && imageInput.files.length > 0) {
                  const reader = new FileReader();
                  reader.onload = function (e) {
                    previewImage.src = e.target.result;
                    previewImageContainer.classList.remove('d-none');
                  };
                  reader.readAsDataURL(imageInput.files[0]);
                } else {
                  previewImageContainer.classList.add('d-none');
                }

                // Show preview modal
                try {
                  // Önce mevcut bir instance var mı kontrol et
                  let previewModalInstance = bootstrap.Modal.getInstance(previewModal);

                  // Eğer yoksa yeni bir instance oluştur
                  if (!previewModalInstance) {
                    previewModalInstance = new bootstrap.Modal(previewModal, {
                      backdrop: true,
                      keyboard: true,
                      focus: true
                    });
                  }

                  // Modal'ı göster
                  previewModalInstance.show();
                } catch (error) {
                  console.error('Önizleme modalı oluşturma hatası:', error);
                  alert('Önizleme modalı açılamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
                }

                // Add confirm send button listener
                const confirmSendBtn = document.getElementById('confirmSendBtn');
                confirmSendBtn.onclick = async () => {
                  try {
                    await axios.post(`${API_BASE_URL}/notifications`, formData, {
                      headers: {
                        'Content-Type': 'multipart/form-data'
                      }
                    });

                    // Hide modals
                    const previewModalInstance = bootstrap.Modal.getInstance(previewModal);
                    if (previewModalInstance) {
                      previewModalInstance.hide();
                      // Modal backdrop'ı temizle
                      document.body.classList.remove('modal-open');
                      const backdrops = document.querySelectorAll('.modal-backdrop');
                      backdrops.forEach(backdrop => backdrop.remove());
                    }

                    const createModal = document.getElementById('createNotificationModal');
                    const modalInstance = bootstrap.Modal.getInstance(createModal);
                    if (modalInstance) {
                      modalInstance.hide();
                      // Modal backdrop'ı temizle
                      document.body.classList.remove('modal-open');
                      const backdrops = document.querySelectorAll('.modal-backdrop');
                      backdrops.forEach(backdrop => backdrop.remove());
                    }

                    // Reset form
                    const notificationForm = document.getElementById('createNotificationForm');
                    if (notificationForm) {
                      notificationForm.reset();
                    }

                    // Reset event handler
                    createBtn.onclick = originalClickHandler;

                    // Clear global variable
                    delete window.contestWinners;

                    // Reload notifications and navigate to notifications page
                    loadNotifications();

                    // Navigate to notifications page
                    navigateTo('notifications');

                    // Show success message
                    alert(i18next.t('app.contestDetail.notifyWinnersSuccess') || 'Kazananlara bildirim başarıyla gönderildi.');
                  } catch (error) {
                    console.error('Create notification error:', error);
                    const errorElement = document.getElementById('notificationError');
                    errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.notifications.createError');
                    errorElement.classList.remove('d-none');
                  }
                };
              } else {
                // Send directly
                await axios.post(`${API_BASE_URL}/notifications`, formData, {
                  headers: {
                    'Content-Type': 'multipart/form-data'
                  }
                });

                // Hide modal
                const createModal = document.getElementById('createNotificationModal');
                const modalInstance = bootstrap.Modal.getInstance(createModal);
                if (modalInstance) {
                  modalInstance.hide();
                  // Modal backdrop'ı temizle
                  document.body.classList.remove('modal-open');
                  const backdrops = document.querySelectorAll('.modal-backdrop');
                  backdrops.forEach(backdrop => backdrop.remove());
                }

                // Reset form
                const notificationForm = document.getElementById('createNotificationForm');
                if (notificationForm) {
                  notificationForm.reset();
                }

                // Reset event handler
                createBtn.onclick = originalClickHandler;

                // Clear global variable
                delete window.contestWinners;

                // Reload notifications and navigate to notifications page
                loadNotifications();

                // Navigate to notifications page
                navigateTo('notifications');

                // Show success message
                alert(i18next.t('app.contestDetail.notifyWinnersSuccess') || 'Kazananlara bildirim başarıyla gönderildi.');
              }
            } catch (error) {
              console.error('Create notification error:', error);
              const errorElement = document.getElementById('notificationError');
              errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.notifications.createError');
              errorElement.classList.remove('d-none');
            }
          };
        }
      } catch (error) {
        console.error('Event listener ekleme hatası:', error);
        alert('Bildirim oluşturma işlemi sırasında bir hata oluştu. Lütfen sayfayı yenileyip tekrar deneyin.');
      }
    }, 500); // setTimeout için kapanış
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    alert('Bildirim oluşturma işlemi sırasında bir hata oluştu. Lütfen sayfayı yenileyip tekrar deneyin.');
  }
}

// Initialize notification form
function initNotificationForm() {
  console.log('Notification form initialized');
  // Form reset
  const form = document.getElementById('createNotificationForm');
  if (form) {
    form.reset();
  }

  // Temizle buton container
  const buttonContainer = document.getElementById('notificationButtons');
  if (buttonContainer) {
    buttonContainer.innerHTML = '';
  }
}

// Initialize notification filters
function initNotificationFilters() {
  const statusFilter = document.getElementById('statusFilter');
  if (statusFilter) {
    statusFilter.addEventListener('change', () => {
      filterNotifications(1, statusFilter.value);
    });
  }
}

// Initialize notification refresh
function initNotificationRefresh() {
  const refreshBtn = document.querySelector('.btn-secondary[onclick="loadNotifications()"]');
  if (refreshBtn) {
    console.log('Refresh button found');
  }
}

// Show create notification modal
function showCreateNotificationModal() {
  // Get the modal element
  const modal = document.getElementById('createNotificationModal');

  // Check if modal exists
  if (!modal) {
    console.error('Create notification modal not found');
    return;
  }

  // Create a new Bootstrap modal instance and show it
  try {
    // Önce mevcut bir instance var mı kontrol et
    let modalInstance = bootstrap.Modal.getInstance(modal);

    // Eğer yoksa yeni bir instance oluştur
    if (!modalInstance) {
      modalInstance = new bootstrap.Modal(modal, {
        backdrop: true,
        keyboard: true,
        focus: true
      });
    }

    // Modal'ı göster
    modalInstance.show();
  } catch (error) {
    console.error('Modal oluşturma hatası:', error);
    alert('Bildirim oluşturma modalı açılamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
  }
  // Bildirim formunu başlat
  initNotificationForm();

  // Bildirim filtrelerini başlat
  initNotificationFilters();

  // Bildirim yenileme butonunu başlat
  initNotificationRefresh();
}

// Initialize notification modal listeners
function initNotificationModalListeners() {
  // Get the create notification button
  const createBtn = document.getElementById('createNotificationBtn');
  if (createBtn) {
    createBtn.addEventListener('click', async () => {
      // Eğer bu bir kazananlara bildirim gönderme işlemi ise ve daha önce işlenmediyse
      if (document.body.getAttribute('data-winner-notification') === 'true') {
        // İşlem yapıldığını belirtmek için flag'i temizle
        document.body.removeAttribute('data-winner-notification');
        return; // Çift bildirim oluşturma sorununu önlemek için burada çık
      }
      const modal = document.getElementById('createNotificationModal');
      const form = document.getElementById('createNotificationForm');
      const title = document.getElementById('notificationTitle').value;
      const message = document.getElementById('notificationMessage').value;
      const errorElement = document.getElementById('notificationError');

      // Form doğrulama
      errorElement.classList.add('d-none');

      // Başlık ve mesaj kontrolü
      if (!title.trim()) {
        errorElement.textContent = i18next.t('app.notifications.titleRequired') || 'Başlık alanı zorunludur';
        errorElement.classList.remove('d-none');
        return;
      }

      if (!message.trim()) {
        errorElement.textContent = i18next.t('app.notifications.messageRequired') || 'Mesaj alanı zorunludur';
        errorElement.classList.remove('d-none');
        return;
      }

      // Get URLs
      const urls = [];
      document.querySelectorAll('.notification-url-input').forEach(input => {
        if (input.value.trim()) {
          urls.push(input.value.trim());
        }
      });

      // Get buttons
      const buttons = [];
      let invalidButtonUrl = false;
      let invalidButtonText = false;

      document.querySelectorAll('.notification-button-group').forEach((group) => {
        const textInput = group.querySelector('.notification-button-text');
        const urlInput = group.querySelector('.notification-button-url');

        // Buton metni kontrolü
        if (textInput && !textInput.value.trim() && urlInput && urlInput.value.trim()) {
          invalidButtonText = true;
          return;
        }

        // Buton URL kontrolü
        if (urlInput && textInput && textInput.value.trim() && !urlInput.value.trim()) {
          invalidButtonUrl = true;
          return;
        }

        // URL formatı kontrolü
        if (urlInput && urlInput.value.trim()) {
          try {
            new URL(urlInput.value.trim());
          } catch (e) {
            invalidButtonUrl = true;
            return;
          }
        }

        if (textInput && urlInput && textInput.value.trim() && urlInput.value.trim()) {
          buttons.push({
            text: textInput.value.trim(),
            url: urlInput.value.trim()
          });
        }
      });

      // Buton doğrulama hataları
      if (invalidButtonText) {
        errorElement.textContent = i18next.t('app.notifications.buttonTextRequired') || 'Buton metni alanı zorunludur';
        errorElement.classList.remove('d-none');
        return;
      }

      if (invalidButtonUrl) {
        errorElement.textContent = i18next.t('app.notifications.buttonUrlInvalid') || 'Buton URL alanı geçerli bir URL olmalıdır';
        errorElement.classList.remove('d-none');
        return;
      }

      // Get image if exists
      const imageInput = document.getElementById('notificationImage');
      const formData = new FormData();
      formData.append('title', title);
      formData.append('message', message);

      // Önemli: urls'i string olarak değil, her bir URL'i ayrı ayrı ekle
      if (urls.length > 0) {
        urls.forEach((url, index) => {
          formData.append(`urls[${index}]`, url);
        });
      }

      // Butonları ekle
      if (buttons.length > 0) {
        formData.append('buttons', JSON.stringify(buttons));
      }

      if (imageInput && imageInput.files.length > 0) {
        formData.append('image', imageInput.files[0]);
      }

      try {
        // Check if preview is required
        if (document.getElementById('previewBeforeSend').checked) {
          // Show preview
          const previewModal = document.getElementById('previewNotificationModal');
          const previewMessageContent = document.getElementById('previewMessageContent');
          const previewImageContainer = document.getElementById('previewImageContainer');
          const previewImage = document.getElementById('previewImage');

          // Set preview content
          previewMessageContent.innerHTML = message.replace(/\n/g, '<br>');

          // Show/hide image preview
          if (imageInput && imageInput.files.length > 0) {
            const reader = new FileReader();
            reader.onload = function (e) {
              previewImage.src = e.target.result;
              previewImageContainer.classList.remove('d-none');
            };
            reader.readAsDataURL(imageInput.files[0]);
          } else {
            previewImageContainer.classList.add('d-none');
          }

          // Show preview modal
          try {
            // Önce mevcut bir instance var mı kontrol et
            let previewModalInstance = bootstrap.Modal.getInstance(previewModal);

            // Eğer yoksa yeni bir instance oluştur
            if (!previewModalInstance) {
              previewModalInstance = new bootstrap.Modal(previewModal, {
                backdrop: true,
                keyboard: true,
                focus: true
              });
            }

            // Modal'ı göster
            previewModalInstance.show();
          } catch (error) {
            console.error('Önizleme modalı oluşturma hatası:', error);
            alert('Önizleme modalı açılamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
          }

          // Add confirm send button listener
          const confirmSendBtn = document.getElementById('confirmSendBtn');
          confirmSendBtn.onclick = async () => {
            try {
              await axios.post(`${API_BASE_URL}/notifications`, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              });

              // Hide modals
              const previewModalInstance = bootstrap.Modal.getInstance(previewModal);
              if (previewModalInstance) previewModalInstance.hide();

              const createModal = document.getElementById('createNotificationModal');
              const modalInstance = bootstrap.Modal.getInstance(createModal);
              if (modalInstance) {
                modalInstance.hide();
                // Modal backdrop'ı temizle
                document.body.classList.remove('modal-open');
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
              }

              // Reset form
              const notificationForm = document.getElementById('createNotificationForm');
              if (notificationForm) {
                notificationForm.reset();
              }

              // Reload notifications and navigate to notifications page
              loadNotifications();

              // Navigate to notifications page
              loadNotifications();
            } catch (error) {
              console.error('Create notification error:', error);
              const errorElement = document.getElementById('notificationError');
              errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.notifications.createError');
              errorElement.classList.remove('d-none');
            }
          };
        } else {
          // Send directly
          await axios.post(`${API_BASE_URL}/notifications`, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });

          // Hide modal
          const modalInstance = bootstrap.Modal.getInstance(modal);
          if (modalInstance) modalInstance.hide();

          // Reset form
          form.reset();

          // Reload notifications and navigate to notifications page
          loadNotifications();

          // Navigate to notifications page
          loadNotifications();
        }
      } catch (error) {
        console.error('Create notification error:', error);
        const errorElement = document.getElementById('notificationError');
        errorElement.textContent = error.response?.data?.message || error.message || i18next.t('app.notifications.createError');
        errorElement.classList.remove('d-none');
      }
    });
  }

  // Add URL button
  const addUrlBtn = document.getElementById('addUrlBtn');
  if (addUrlBtn) {
    addUrlBtn.addEventListener('click', () => {
      const urlsContainer = document.getElementById('notificationUrls');
      const urlCount = urlsContainer.querySelectorAll('.notification-url-input').length + 1;

      const urlGroup = document.createElement('div');
      urlGroup.className = 'input-group mb-2';
      urlGroup.innerHTML = `
        <div class="input-group-prepend">
          <span class="input-group-text">URL ${urlCount}</span>
        </div>
        <input type="url" class="form-control notification-url-input" placeholder="https://example.com">
        <div class="input-group-append">
          <button type="button" class="btn btn-danger remove-url-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;

      urlsContainer.appendChild(urlGroup);

      // URL remove button
      urlGroup.querySelector('.remove-url-btn').addEventListener('click', () => {
        urlGroup.remove();
      });
    });
  }

  // Add Button button
  const addButtonBtn = document.getElementById('addButtonBtn');
  if (addButtonBtn) {
    addButtonBtn.addEventListener('click', () => {
      const buttonsContainer = document.getElementById('notificationButtons');
      const buttonCount = buttonsContainer.querySelectorAll('.notification-button-group').length + 1;

      // Maksimum 10 buton kontrolü
      if (buttonCount > 10) {
        alert('Maksimum 10 buton ekleyebilirsiniz.');
        return;
      }

      const buttonGroup = document.createElement('div');
      buttonGroup.className = 'notification-button-group mb-2';
      buttonGroup.innerHTML = `
        <div class="input-group mb-1">
          <div class="input-group-prepend">
            <span class="input-group-text">Buton ${buttonCount} Metni</span>
          </div>
          <input type="text" class="form-control notification-button-text" placeholder="Buton metni">
          <div class="input-group-append">
            <button type="button" class="btn btn-danger remove-button-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="input-group">
          <div class="input-group-prepend">
            <span class="input-group-text">Buton ${buttonCount} URL</span>
          </div>
          <input type="url" class="form-control notification-button-url" placeholder="https://example.com">
        </div>
      `;

      buttonsContainer.appendChild(buttonGroup);

      // Button remove button
      buttonGroup.querySelector('.remove-button-btn').addEventListener('click', () => {
        buttonGroup.remove();

        // Buton numaralarını güncelle
        buttonsContainer.querySelectorAll('.notification-button-group').forEach((group, index) => {
          const buttonTextLabel = group.querySelector('.input-group:first-child .input-group-text');
          const buttonUrlLabel = group.querySelector('.input-group:last-child .input-group-text');

          if (buttonTextLabel) {
            buttonTextLabel.textContent = `Buton ${index + 1} Metni`;
          }

          if (buttonUrlLabel) {
            buttonUrlLabel.textContent = `Buton ${index + 1} URL`;
          }
        });
      });
    });
  }

  // Image preview
  const imageInput = document.getElementById('notificationImage');
  const imagePreviewContainer = document.getElementById('imagePreviewContainer');
  const imagePreview = document.getElementById('imagePreview');
  const removeImageBtn = document.getElementById('removeImageBtn');

  if (imageInput) {
    imageInput.addEventListener('change', () => {
      if (imageInput.files.length > 0) {
        const reader = new FileReader();
        reader.onload = function (e) {
          imagePreview.src = e.target.result;
          imagePreviewContainer.classList.remove('d-none');
        };
        reader.readAsDataURL(imageInput.files[0]);
      } else {
        imagePreviewContainer.classList.add('d-none');
      }
    });
  }

  if (removeImageBtn) {
    removeImageBtn.addEventListener('click', () => {
      imageInput.value = '';
      imagePreviewContainer.classList.add('d-none');
    });
  }
}

// View notification details
async function viewNotificationDetails(notificationId) {
  try {
    const response = await axios.get(`${API_BASE_URL}/notifications/${notificationId}`);
    console.log('Bildirim detayları:', response.data);

    // API yanıtını kontrol et
    if (!response.data || !response.data.notification) {
      console.error('API yanıtında notification nesnesi bulunamadı:', response.data);
      alert('Bildirim detayları alınamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }

    const notification = response.data.notification;

    // Get modal
    const modal = document.getElementById('notificationDetailModal');
    if (!modal) {
      console.error('Notification detail modal not found');
      return;
    }

    // Set modal content
    const modalContent = document.getElementById('notificationDetailContent');

    // Format dates
    const createdAt = new Date(notification.createdAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US');
    const startedAt = notification.startedAt ? new Date(notification.startedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US') : '-';
    const completedAt = notification.completedAt ? new Date(notification.completedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US') : '-';

    // Set content
    modalContent.innerHTML = `
      <div class="row mb-3">
        <div class="col-md-6">
          <p><strong data-i18n="app.notifications.status">Durum:</strong> ${getNotificationStatusBadge(notification.status)}</p>
          <p><strong data-i18n="app.notifications.createdAt">Oluşturulma Tarihi:</strong> ${createdAt}</p>
          <p><strong data-i18n="app.notifications.startedAt">Başlama Tarihi:</strong> ${startedAt}</p>
        </div>
        <div class="col-md-6">
          <p><strong data-i18n="app.notifications.completedAt">Tamamlanma Tarihi:</strong> ${completedAt}</p>
          <p><strong data-i18n="app.notifications.totalRecipients">Toplam Alıcı:</strong> ${notification.totalRecipients}</p>
          <p><strong data-i18n="app.notifications.progress">İlerleme:</strong> ${notification.successCount + notification.failedCount}/${notification.totalRecipients}</p>
        </div>
      </div>

      <div class="card mb-3">
        <div class="card-header bg-light">
          <strong data-i18n="app.notifications.message">Mesaj</strong>
        </div>
        <div class="card-body">
          <p>${notification.message.replace(/\n/g, '<br>')}</p>
        </div>
      </div>

      ${notification.urls && notification.urls.length > 0 ? `
        <div class="card mb-3">
          <div class="card-header bg-light">
            <strong data-i18n="app.notifications.urls">URL'ler</strong>
          </div>
          <div class="card-body">
            <ul>
              ${notification.urls.map((url, index) => `
                <li><strong>URL ${index + 1}:</strong> <a href="${url}" target="_blank">${url}</a></li>
              `).join('')}
            </ul>
          </div>
        </div>
      ` : ''}

      ${notification.buttons && notification.buttons.length > 0 ? `
        <div class="card mb-3">
          <div class="card-header bg-light">
            <strong data-i18n="app.notifications.buttons">Butonlar</strong>
          </div>
          <div class="card-body">
            <ul class="list-group">
              ${notification.buttons.map((button, index) => `
                <li class="list-group-item">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <strong>Buton ${index + 1}:</strong> ${button.text}
                      <br>
                      <small><a href="${button.url}" target="_blank">${button.url}</a></small>
                    </div>
                    <span class="badge bg-info">${button.clicks || 0} tıklama</span>
                  </div>
                </li>
              `).join('')}
            </ul>
          </div>
        </div>
      ` : ''}

      ${notification.image ? `
        <div class="card mb-3">
          <div class="card-header bg-light">
            <strong data-i18n="app.notifications.image">Fotoğraf</strong>
          </div>
          <div class="card-body text-center">
            <img src="${notification.image.startsWith('/') ? notification.image : '/' + notification.image}" class="img-fluid img-thumbnail" style="max-height: 300px;" onerror="this.onerror=null; this.src='${API_BASE_URL}/notifications/${notification._id}/image';">
          </div>
        </div>
      ` : ''}
    `;

    // Show modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // Update translations
    updateUI();
  } catch (error) {
    console.error('View notification details error:', error);
    alert(i18next.t('app.notifications.detailError'));
  }
}

// Send notification
async function sendNotification(notificationId) {
  if (!confirm(i18next.t('app.notifications.sendConfirm'))) {
    return;
  }

  try {
    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/send`);
    loadNotifications();
  } catch (error) {
    console.error('Send notification error:', error);
    alert(error.response?.data?.message || error.message || i18next.t('app.notifications.sendError'));
  }
}

// Pause notification
async function pauseNotification(notificationId) {
  if (!confirm(i18next.t('app.notifications.pauseConfirm'))) {
    return;
  }

  try {
    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/pause`);
    loadNotifications();
  } catch (error) {
    console.error('Pause notification error:', error);
    alert(error.response?.data?.message || error.message || i18next.t('app.notifications.pauseError'));
  }
}

// Resume notification
async function resumeNotification(notificationId) {
  if (!confirm(i18next.t('app.notifications.resumeConfirm'))) {
    return;
  }

  try {
    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/resume`);
    loadNotifications();
  } catch (error) {
    console.error('Resume notification error:', error);
    alert(error.response?.data?.message || error.message || i18next.t('app.notifications.resumeError'));
  }
}

// Stop notification
async function stopNotification(notificationId) {
  if (!confirm(i18next.t('app.notifications.stopConfirm'))) {
    return;
  }

  try {
    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/stop`);
    loadNotifications();
  } catch (error) {
    console.error('Stop notification error:', error);
    alert(error.response?.data?.message || error.message || i18next.t('app.notifications.stopError'));
  }
}

// Retry failed notifications
async function retryFailedNotification(notificationId) {
  if (!confirm(i18next.t('app.notifications.retryConfirm') || 'Başarısız bildirimleri yeniden göndermek istediğinize emin misiniz?')) {
    return;
  }

  try {
    // Önce bildirimi kontrol et
    const checkResponse = await axios.get(`${API_BASE_URL}/notifications/${notificationId}`);
    console.log('Bildirim detayları:', checkResponse.data);

    // API yanıtını kontrol et
    if (!checkResponse.data || !checkResponse.data.notification) {
      console.error('API yanıtında notification nesnesi bulunamadı:', checkResponse.data);
      alert('Bildirim detayları alınamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }

    const notification = checkResponse.data.notification;
    console.log('Bildirim:', notification);

    // retryCount değerini kontrol et
    if (notification.retryCount === undefined) {
      console.warn('Bildirimde retryCount özelliği bulunamadı. Varsayılan olarak 0 kullanılıyor.');
    }

    // Maksimum deneme sayısını kontrol et
    if ((notification.retryCount || 0) >= 3) {
      alert('Bu bildirim için maksimum yeniden gönderme sayısına ulaşıldı (3/3). Daha fazla deneme yapılamaz.');
      return;
    }

    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/retry`);
    alert(i18next.t('app.notifications.retrySuccess') || 'Başarısız bildirimler yeniden gönderiliyor');
    loadNotifications();
  } catch (error) {
    console.error('Retry failed notifications error:', error);
    alert(error.response?.data?.message || error.message || i18next.t('app.notifications.retryError') || 'Başarısız bildirimleri yeniden gönderirken bir hata oluştu');
  }
}

// Resend notification (for FAILED notifications)
async function resendNotification(notificationId) {
  if (!confirm(i18next.t('app.notifications.resendConfirm') || 'Bildirimi yeniden göndermek istediğinize emin misiniz?')) {
    return;
  }

  try {
    // Önce bildirimi kontrol et
    const checkResponse = await axios.get(`${API_BASE_URL}/notifications/${notificationId}`);
    console.log('Bildirim detayları:', checkResponse.data);

    // API yanıtını kontrol et
    if (!checkResponse.data || !checkResponse.data.notification) {
      console.error('API yanıtında notification nesnesi bulunamadı:', checkResponse.data);
      alert('Bildirim detayları alınamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }

    const notification = checkResponse.data.notification;
    console.log('Bildirim:', notification);

    // retryCount değerini kontrol et
    if (notification.retryCount === undefined) {
      console.warn('Bildirimde retryCount özelliği bulunamadı. Varsayılan olarak 0 kullanılıyor.');
    }

    // Maksimum deneme sayısını kontrol et
    if ((notification.retryCount || 0) >= 3) {
      alert('Bu bildirim için maksimum yeniden gönderme sayısına ulaşıldı (3/3). Daha fazla deneme yapılamaz.');
      return;
    }

    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/resend`);
    alert(i18next.t('app.notifications.resendSuccess') || 'Bildirim yeniden gönderiliyor');
    loadNotifications();
  } catch (error) {
    console.error('Resend notification error:', error);
    alert(error.response?.data?.message || error.message || i18next.t('app.notifications.resendError') || 'Bildirimi yeniden gönderirken bir hata oluştu');
  }
}

// Retry single recipient
async function retrySingleRecipient(notificationId, userId) {
  if (!confirm('Bu alıcıya bildirimi yeniden göndermek istediğinize emin misiniz?')) {
    return;
  }

  try {
    // Önce bildirimi kontrol et
    const checkResponse = await axios.get(`${API_BASE_URL}/notifications/${notificationId}`);
    console.log('Bildirim detayları:', checkResponse.data);

    // API yanıtını kontrol et
    if (!checkResponse.data || !checkResponse.data.notification) {
      console.error('API yanıtında notification nesnesi bulunamadı:', checkResponse.data);
      alert('Bildirim detayları alınamadı. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }

    const notification = checkResponse.data.notification;
    console.log('Bildirim:', notification);

    // retryCount değerini kontrol et
    if (notification.retryCount === undefined) {
      console.warn('Bildirimde retryCount özelliği bulunamadı. Varsayılan olarak 0 kullanılıyor.');
    }

    // Maksimum deneme sayısını kontrol et
    if ((notification.retryCount || 0) >= 3) {
      alert('Bu bildirim için maksimum yeniden gönderme sayısına ulaşıldı (3/3). Daha fazla deneme yapılamaz.');
      return;
    }

    // Alıcıyı kontrol et
    let recipient = null;
    for (const r of notification.recipients) {
      if (r.user && (r.user._id === userId || String(r.user) === userId)) {
        recipient = r;
        break;
      }
    }

    if (recipient && (recipient.retryCount || 0) >= 3) {
      alert('Bu alıcı için maksimum yeniden gönderme sayısına ulaşıldı (3/3). Daha fazla deneme yapılamaz.');
      return;
    }

    // Özel endpoint oluşturalım
    await axios.post(`${API_BASE_URL}/notifications/${notificationId}/retry-single`, { userId });
    alert('Bildirim alıcıya yeniden gönderiliyor');

    // Rapor modalını kapat ve bildirimleri yenile
    const reportModal = document.getElementById('notificationReportModal');
    if (reportModal) {
      const modalInstance = bootstrap.Modal.getInstance(reportModal);
      if (modalInstance) modalInstance.hide();
    }

    loadNotifications();
  } catch (error) {
    console.error('Retry single recipient error:', error);
    alert(error.response?.data?.message || error.message || 'Bildirimi yeniden gönderirken bir hata oluştu');
  }
}

// View notification report
async function viewNotificationReport(notificationId) {
  try {
    const response = await axios.get(`${API_BASE_URL}/notifications/${notificationId}/report`);
    const report = response.data;

    // Get modal
    const modal = document.getElementById('notificationReportModal');
    if (!modal) {
      console.error('Notification report modal not found');
      return;
    }

    // Set modal content
    const modalContent = document.getElementById('notificationReportContent');

    // Sayaçları manuel olarak hesapla
    let successCount = 0;
    let failedCount = 0;
    let pendingCount = 0;
    let retryCount = 0;

    report.deliveries.forEach(delivery => {
      if (delivery.status === 'SUCCESS') {
        successCount++;
      } else if (delivery.status === 'FAILED') {
        failedCount++;
      } else if (delivery.status === 'RETRY') {
        retryCount++;
      } else {
        pendingCount++;
      }
    });

    // Toplam bekleyen sayısı = bekleyen + yeniden deneme
    const totalPending = pendingCount + retryCount;

    // Set content
    modalContent.innerHTML = `
      <div class="alert alert-info mb-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h5>${report.notification.title}</h5>
            <p class="mb-0"><strong data-i18n="app.notifications.status">Durum:</strong> ${getNotificationStatusBadge(report.notification.status)}</p>
          </div>
          ${failedCount > 0 ? `
          <div>
            <button class="btn btn-warning ${(report.notification.retryCount || 0) >= 3 ? 'disabled' : ''}"
              onclick="${(report.notification.retryCount || 0) >= 3 ? 'return false' : `retryFailedNotification('${report.notification.id}'); bootstrap.Modal.getInstance(document.getElementById('notificationReportModal')).hide();`}"
              ${(report.notification.retryCount || 0) >= 3 ? 'disabled' : ''}>
              <i class="fas fa-redo-alt"></i> Başarısız Bildirimleri Yeniden Gönder${report.notification.retryCount ? ` (${report.notification.retryCount}/3)` : ''}
            </button>
          </div>
          ` : ''}
          ${report.notification.status === 'FAILED' ? `
          <div>
            <button class="btn btn-primary ${(report.notification.retryCount || 0) >= 3 ? 'disabled' : ''}"
              onclick="${(report.notification.retryCount || 0) >= 3 ? 'return false' : `resendNotification('${report.notification.id}'); bootstrap.Modal.getInstance(document.getElementById('notificationReportModal')).hide();`}"
              ${(report.notification.retryCount || 0) >= 3 ? 'disabled' : ''}>
              <i class="fas fa-paper-plane"></i> Bildirimi Yeniden Gönder${report.notification.retryCount ? ` (${report.notification.retryCount}/3)` : ''}
            </button>
          </div>
          ` : ''}
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.notifications.totalRecipients">Toplam Alıcı</h5>
              <p class="card-text display-4">${report.deliveries.length}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.notifications.successCount">Başarılı</h5>
              <p class="card-text display-4">${successCount}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.notifications.failedCount">Başarısız</h5>
              <p class="card-text display-4">${failedCount}</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <h5 class="card-title" data-i18n="app.notifications.pendingCount">Bekleyen</h5>
              <p class="card-text display-4">${totalPending}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="alert alert-secondary mb-3">
        <div class="d-flex justify-content-between">
          <div>
            <strong>Başlangıç:</strong> ${report.notification.startedAt ? new Date(report.notification.startedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US') : '-'}
          </div>
          <div>
            <strong>Tamamlanma:</strong> ${report.notification.completedAt ? new Date(report.notification.completedAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US') : '-'}
          </div>
        </div>
      </div>

      <h5 data-i18n="app.notifications.deliveryDetails">Gönderim Detayları</h5>

      <div class="mb-3">
        <div class="input-group">
          <input type="text" class="form-control" id="reportSearchInput" placeholder="Kullanıcı adı veya Telegram ID ile ara...">
          <button class="btn btn-outline-secondary" type="button" onclick="filterReportTable()">
            <i class="fas fa-search"></i> Ara
          </button>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-striped" id="reportTable">
          <thead>
            <tr>
              <th data-i18n="app.notifications.username">Kullanıcı Adı</th>
              <th data-i18n="app.notifications.telegramId">Telegram ID</th>
              <th data-i18n="app.notifications.status">Durum</th>
              <th data-i18n="app.notifications.sentAt">Gönderim Tarihi</th>
              <th data-i18n="app.notifications.error">Hata</th>
              <th data-i18n="app.notifications.actions">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            ${report.deliveries.map(delivery => {
      // Durum badge'ini belirle
      let statusBadge = '';
      let statusClass = '';

      if (delivery.status === 'SUCCESS') {
        statusBadge = 'Başarılı';
        statusClass = 'bg-success';
      } else if (delivery.status === 'FAILED') {
        statusBadge = 'Başarısız';
        statusClass = 'bg-danger';
      } else if (delivery.status === 'RETRY') {
        statusBadge = 'Yeniden Deneniyor';
        statusClass = 'bg-warning text-dark';
      } else {
        statusBadge = 'Bekliyor';
        statusClass = 'bg-secondary';
      }

      // Yeniden deneme bilgisi
      const retryInfo = delivery.retryCount > 0 ?
        `<span class="badge bg-info ms-1">${delivery.retryCount}/3 deneme</span>` : '';

      // Kullanıcı adı ve Telegram ID kontrolü
      const username = delivery.user && delivery.user.username ? delivery.user.username : '-';
      const telegramId = delivery.user && delivery.user.telegramId ? delivery.user.telegramId : '-';

      // Durum badge'ini oluştur
      const statusBadgeHtml = `<span class="badge ${statusClass}">${statusBadge}</span>${retryInfo}`;

      // Başarısız bildirimler için yeniden deneme butonu ekle
      // Eğer bildirim 3 kez denenmiş veya alıcı 3 kez denenmiş ise butonu devre dışı bırak
      const isDisabled = (report.notification.retryCount || 0) >= 3 || ((delivery.retryCount || 0) >= 3);
      const retryButton = delivery.status === 'FAILED' ?
        `<button class="btn btn-sm btn-warning retry-single-btn ${isDisabled ? 'disabled' : ''}"
          data-user-id="${delivery.user?._id || ''}"
          data-notification-id="${report.notification.id}"
          ${isDisabled ? 'disabled' : ''}>
          <i class="fas fa-redo-alt"></i> Yeniden Dene${delivery.retryCount ? ` (${delivery.retryCount}/3)` : ''}
        </button>` : '';

      return `
              <tr>
                <td>${username}</td>
                <td>${telegramId}</td>
                <td>${statusBadgeHtml}</td>
                <td>${delivery.sentAt ? new Date(delivery.sentAt).toLocaleString(i18next.language === 'tr' ? 'tr-TR' : 'en-US') : '-'}</td>
                <td>${delivery.error || '-'}</td>
                <td>${retryButton}</td>
              </tr>
              `;
    }).join('')}
          </tbody>
        </table>
      </div>
    `;

    // Show modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // Yeniden deneme butonlarına tıklama olayı ekle
    setTimeout(() => {
      const retryButtons = document.querySelectorAll('.retry-single-btn');
      retryButtons.forEach(button => {
        button.addEventListener('click', function () {
          const notificationId = this.getAttribute('data-notification-id');
          const userId = this.getAttribute('data-user-id');
          retrySingleRecipient(notificationId, userId);
        });
      });
      console.log(`${retryButtons.length} adet yeniden deneme butonu bulundu ve event listener eklendi.`);
    }, 500);

    // Arama input'una Enter tuşu event listener'ı ekle
    const searchInput = document.getElementById('reportSearchInput');
    if (searchInput) {
      searchInput.addEventListener('keyup', function (event) {
        if (event.key === 'Enter') {
          filterReportTable();
        }
      });
    }

    // Update translations
    updateUI();
  } catch (error) {
    console.error('View notification report error:', error);
    alert(i18next.t('app.notifications.reportError'));
  }
}

// Filter report table
function filterReportTable() {
  const input = document.getElementById('reportSearchInput');
  const filter = input.value.toUpperCase();
  const table = document.getElementById('reportTable');
  const tr = table.getElementsByTagName('tr');

  // Loop through all table rows, and hide those who don't match the search query
  for (let i = 1; i < tr.length; i++) { // Start from 1 to skip header row
    const tdUsername = tr[i].getElementsByTagName('td')[0];
    const tdTelegramId = tr[i].getElementsByTagName('td')[1];

    if (tdUsername && tdTelegramId) {
      const usernameValue = tdUsername.textContent || tdUsername.innerText;
      const telegramIdValue = tdTelegramId.textContent || tdTelegramId.innerText;

      if (usernameValue.toUpperCase().indexOf(filter) > -1 || telegramIdValue.toUpperCase().indexOf(filter) > -1) {
        tr[i].style.display = '';
      } else {
        tr[i].style.display = 'none';
      }
    }
  }
}

// init() is now called from index.html after app.js is loaded
