{"app": {"common": {"loading": "Loading...", "error": "An error occurred. Please try again.", "success": "Operation completed successfully.", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "back": "Back", "yes": "Yes", "no": "No", "confirm": "Confirm", "active": "Active", "inactive": "Inactive", "draft": "Draft", "completed": "Completed", "none": "None", "welcome": "Welcome", "refresh": "Refresh", "close": "Close", "all": "All", "actions": "Actions"}, "title": "Sports Prediction Contest", "login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "button": "<PERSON><PERSON>", "error": "Invalid username or password", "inactive": "Your account has been disabled. Please contact an administrator."}, "nav": {"dashboard": "Dashboard", "contests": "Contests", "users": "Users", "notifications": "Notifications", "settings": "<PERSON><PERSON>", "admins": "Administrators", "logout": "Logout"}, "dashboard": {"title": "Dashboard", "activeContests": "Active Contests", "totalUsers": "Total Users", "totalSubmissions": "Total Submissions", "recentContests": "Recent Contests", "noContests": "No contests available yet", "newUsers": "New Users (7d)", "activeUsers": "Active Users (7d)"}, "admins": {"title": "Administrators", "create": "Add New Administrator", "username": "Username", "name": "Name", "email": "Email", "role": "Role", "statusLabel": "Status", "lastLogin": "Last Login", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "changePassword": "Change Password", "noAdmins": "No administrators found", "error": "An error occurred while loading administrators. Please try again.", "roles": {"admin": "Administrator", "superadmin": "Super Administrator"}, "status": {"active": "Active", "inactive": "Inactive"}}, "adminDetail": {"title": "Administrator Details", "back": "Return to Administrator List", "info": "Administrator Information", "username": "Username:", "name": "Name:", "email": "Email:", "role": "Role:", "status": "Status:", "lastLogin": "Last Login:", "error": "An error occurred while loading administrator details. Please try again."}, "adminForm": {"createTitle": "Add New Administrator", "editTitle": "Edit Administrator", "username": "Username", "password": "Password", "email": "Email", "name": "Name", "role": "Role", "isActive": "Active", "save": "Add Administrator", "saveChanges": "Save Changes", "cancel": "Cancel", "createError": "An error occurred while creating the administrator. Please try again.", "editError": "An error occurred while updating the administrator. Please try again.", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "changePassword": "Change Password", "passwordError": "An error occurred while changing the password. Please try again."}, "contests": {"title": "Contests", "create": "New Contest", "active": "Active", "draft": "Draft", "completed": "Completed", "cancelled": "Cancelled", "status": "Status", "startDate": "Start Date", "endDate": "End Date", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "activate": "Activate", "export": "Export to Excel", "noContests": "No contests found", "confirmDelete": "Are you sure you want to delete this contest?", "confirmActivate": "Are you sure you want to activate this contest?", "dateError": "End date must be after start date", "creator": "Creator", "participants": "Participants", "winners": "Winners", "cancel": "Cancel", "cancelTitle": "Cancel Contest", "cancelConfirmText": "Are you sure you want to cancel this contest? This action cannot be undone.", "cancelReason": "Cancellation Reason", "cancelReasonHelp": "Explain why the contest is being cancelled (optional)", "confirmCancel": "Cancel Contest", "cancelSuccess": "Contest cancelled successfully", "cancelError": "An error occurred while cancelling the contest", "cancelledBy": "Cancelled By", "cancelledAt": "Cancelled At"}, "contestForm": {"createTitle": "Create New Contest", "editTitle": "Edit Contest", "contestInfo": "Contest Information", "basicInfo": "Basic Information", "title": "Title", "description": "Description", "requirements": "Participation Requirements", "prizes": "Prizes", "optional": "(Optional)", "dates": "Dates", "startDate": "Start Date", "endDate": "End Date", "channelRequirement": "Channel Requirements", "requireChannel": "Channel membership required", "channelUsername": "Channel Username", "questions": "Questions", "addQuestion": "Add Question", "removeQuestion": "Remove", "questionText": "Question Text", "options": "Options", "option": "Option", "addOption": "Add Option", "removeOption": "Remove", "optionText": "Option Text", "correctOption": "Correct Option", "minCorrectAnswers": "Minimum Correct Answers Required to Win", "save": "Save", "cancel": "Cancel", "createContest": "Create Contest"}, "contestDetail": {"title": "Contest Details", "status": "Status", "dates": "Dates", "startDate": "Start", "endDate": "End", "participants": "Participants", "questions": "Questions", "question": "Question", "submissions": "Submissions", "back": "Return to Contests", "returnToDetails": "Return to Contest Details", "edit": "Edit", "delete": "Delete", "activate": "Activate", "setAnswers": "Set Correct Answers", "complete": "Complete Contest", "export": "Export to Excel", "options": "Options", "noSubmissions": "No submissions yet", "questionCount": "Number of Questions", "minCorrectAnswers": "Minimum Correct Answers Required to Win", "channelRequirement": "Channel Requirement", "description": "Description", "correctAnswer": "Correct Answer", "requirements": "Participation Requirements", "prizes": "Prizes", "completeConfirm": "Are you sure you want to complete this contest?", "completing": "Completing contest and calculating winners...", "correctAnswersRequired": "You must set correct answers for all questions before completing the contest.", "completeError": "An error occurred while completing the contest:", "setCorrectAnswers": "Set Correct Answers", "setCorrectAnswersInfo": "Select the correct answer for each question. This will be used to determine winners after the contest is completed.", "saveCorrectAnswers": "Save Correct Answers", "correctAnswersError": "An error occurred while saving correct answers. Please try again.", "activateConfirm": "Are you sure you want to activate this contest?", "activateError": "An error occurred while activating the contest", "notifyWinners": "Send Notification to Winners", "notifyWinnersSuccess": "Notification sent to winners successfully", "noWinners": "No winners found in this contest", "notifyWinnersInfo": "The process of sending notifications to winners will start automatically", "saveAndComplete": "Save Correct Answers and Complete Contest", "saveAndCompleteConfirm": "Are you sure you want to save correct answers and complete the contest? This action cannot be undone.", "savingAndCompleting": "Saving correct answers and completing the contest...", "saveAndCompleteError": "An error occurred while saving correct answers or completing the contest:", "returnToAnswers": "Return to Set Correct Answers Page"}, "users": {"title": "Users", "username": "Username", "telegramId": "Telegram ID", "name": "Name", "registrationDate": "Registration Date", "lastActivity": "Last Activity", "actions": "Actions", "view": "View", "noUsers": "No users found", "filters": "Filters", "search": "Search", "state": "State", "stateIdle": "Idle", "stateRegistering": "Registering", "stateAnswering": "Answering", "stateConfirming": "Confirming", "language": "Language", "filter": "Filter", "resetFilters": "Reset Filters"}, "userDetail": {"title": "User Details", "username": "Username", "telegramId": "Telegram ID", "name": "Name", "registrationDate": "Registration Date", "lastActivity": "Last Activity", "submissions": "Submissions", "back": "Return to Users", "noSubmissions": "No submissions yet", "info": "User Information", "status": "Status", "submissionDate": "Submission Date", "correctAnswers": "Correct Answers", "winner": "Winner", "language": "Language", "blockStatus": "Block Status", "blocked": "Blocked", "notBlocked": "Active", "block": "Block User", "unblock": "Unblock User", "blockReason": "Block Reason", "blockReasonHelp": "Explain why the user is being blocked (optional)", "blockModalTitle": "Block User", "confirmBlock": "Block", "blockSuccess": "User blocked successfully", "blockError": "An error occurred while blocking the user", "unblockConfirm": "Are you sure you want to unblock this user?", "unblockSuccess": "User unblocked successfully", "unblockError": "An error occurred while unblocking the user", "blockedBy": "Blocked By", "blockedAt": "Blocked At"}, "submissions": {"title": "Submissions", "totalParticipants": "Total Participants", "noSubmissions": "No submissions yet", "username": "Username", "telegramId": "Telegram ID", "name": "Name", "correctAnswers": "Correct Answers", "winner": "Winner", "winners": "Winners", "submissionDate": "Submission Date", "actions": "Actions", "viewUser": "View User", "viewAnswers": "View Answers", "viewAnswersTitle": "Participant Answers", "answersFor": "Answers for {{username}}", "contest": "Contest", "user": "User", "question": "Question", "userAnswer": "User Answer", "correctAnswer": "Correct Answer", "noAnswer": "No answer provided", "notSet": "Not set", "correct": "Correct", "incorrect": "Incorrect", "answersError": "An error occurred while loading answers", "totalWinners": "Total Winners: {{count}}"}, "settings": {"title": "<PERSON><PERSON>", "general": "General Settings", "botUsername": "<PERSON><PERSON>", "welcomeMessage": "Welcome Message", "welcomeMessageHelp": "This message is shown when users use the bot for the first time. You can use it to welcome users and provide information about the bot.", "botActive": "Bot Active", "channelRequirement": "Channel Requirement", "channelUsernameHelp": "Add @ symbol (e.g., @yourchannel)", "maintenanceMode": "Maintenance Mode", "maintenanceMessage": "Maintenance Message", "save": "Save Settings", "saveSuccess": "Setting<PERSON> saved successfully"}, "changePassword": {"title": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "save": "Change Password", "cancel": "Cancel", "passwordMismatch": "New passwords do not match", "for": "Change Password for", "loadError": "An error occurred while loading the password change form. Please try again."}, "language": {"title": "Language", "tr": "Turkish", "en": "English", "de": "German", "ar": "Arabic"}, "notifications": {"title": "Notifications", "create": "New Notification", "status": "Status", "createdAt": "Created At", "startedAt": "Started At", "completedAt": "Completed At", "totalRecipients": "Total Recipients", "progress": "Progress", "actions": "Actions", "view": "View", "send": "Send", "pause": "Pause", "resume": "Resume", "stop": "Stop", "report": "Report", "noNotifications": "No notifications found", "createTitle": "Create New Notification", "notificationTitle": "Title", "message": "Message", "image": "Photo", "urls": "URLs", "addUrl": "Add URL", "previewBeforeSend": "Preview before send", "createButton": "Create Notification", "cancel": "Cancel", "preview": "Preview", "previewTitle": "Notification Preview", "confirmSend": "Send", "createError": "An error occurred while creating the notification", "sendConfirm": "Are you sure you want to send this notification?", "pauseConfirm": "Are you sure you want to pause this notification?", "resumeConfirm": "Are you sure you want to resume this notification?", "stopConfirm": "Are you sure you want to stop this notification?", "sendError": "An error occurred while sending the notification", "pauseError": "An error occurred while pausing the notification", "resumeError": "An error occurred while resuming the notification", "stopError": "An error occurred while stopping the notification", "detailError": "An error occurred while loading notification details", "reportError": "An error occurred while loading notification report", "successCount": "Successful", "failedCount": "Failed", "pendingCount": "Pending", "deliveryDetails": "Delivery Details", "username": "Username", "telegramId": "Telegram ID", "sentAt": "<PERSON><PERSON>", "error": "Error", "retryCount": "Retry Count", "retryStatus": "Retrying", "maxRetryReached": "Maximum retry count reached", "dailyLimit": "Daily Limit", "dailyLimitReached": "Daily notification sending limit reached", "buttons": "Buttons", "addButton": "<PERSON>d <PERSON>", "buttonText": "Button Text", "buttonUrl": "Button URL", "removeButton": "Remove", "titleLabel": "Title", "titleHelp": "Notification campaign title (Not included in the notification)", "messageLabel": "Message", "messageHelp": "Enter notification message", "titleRequired": "Title field is required", "messageRequired": "Message field is required", "buttonTextRequired": "Button text field is required", "buttonUrlInvalid": "Button URL field must be a valid URL", "imageLabel": "Photo", "imageHelp": "Select a photo to add to the notification", "urlsLabel": "URLs", "urlsHelp": "Enter URLs to add to the notification", "buttonsHelp": "Add buttons you want to include in the notification (maximum 10 buttons). You must specify a text and URL for each button.", "detailTitle": "Notification Details", "list": "Notification List", "filters": "Filters", "statusDraft": "Draft", "statusSending": "Sending", "statusPaused": "Paused", "statusCompleted": "Completed", "statusFailed": "Failed", "removeImage": "Remove Image", "retryConfirm": "Are you sure you want to retry failed notifications?", "retrySuccess": "Failed notifications are being resent", "retryError": "An error occurred while retrying failed notifications", "resendConfirm": "Are you sure you want to resend this notification?", "resendSuccess": "Notification is being resent", "resendError": "An error occurred while resending the notification", "previewMessage": "Message Preview", "previewImage": "Photo Preview", "reportTitle": "Notification Report", "all": "All", "autoRefresh": "Notifications are automatically refreshed every {seconds} seconds", "notifyContestWinners": "Send Notification to Contest Winners", "contestWinnersTitle": "Notification to Contest Winners", "selectContest": "Select Contest", "noCompletedContests": "No completed contests found", "contestHasNoWinners": "No winners found in this contest", "contestWinnersCount": "Winner Count", "retry": "Retry"}}, "bot": {"start": {"welcomeBack": "Welcome back, {{username}}! You can use the /contests command to see current contests or /help for more information.", "register": "To register, please enter your username:"}, "registration": {"complete": "Registration completed! Your username is set to \"{{username}}\".\n\nYou can use the /contests command to see current contests or /help for more information.", "askAgain": "Please enter your username again:", "confirm": "Is this username correct? \"{{username}}\""}, "contests": {"active": "Active", "noActive": "There are no active contests at the moment. Please check again later.", "title": "Active Contests", "select": "Select the contest you want to participate in:", "ends": "Ends", "questions": "Questions"}, "participation": {"notFound": "Contest not found. Please use the /contests command to view active contests.", "notActive": "This contest is no longer active. Please use the /contests command to view active contests.", "notStarted": "This contest has not started yet. Start date: {{date}}", "ended": "This contest has ended. End date: {{date}}", "alreadySubmitted": "You have already participated in this contest. You can only participate once in each contest.", "channelRequired": "You need to be a member of {{channel}} channel to participate in this contest.", "error": "An error occurred while participating in the contest. Please try again later."}, "answering": {"question": "Question {{current}}/{{total}}\n\n{{text}}", "noActiveSubmission": "You don't have an active contest submission. Please use the /contests command to view active contests.", "submissionNotFound": "Your contest submission was not found. Please use the /contests command to view active contests.", "contestNotActive": "This contest is no longer active. Please use the /contests command to view active contests.", "questionNotFound": "Question not found. Please use the /contests command to view active contests.", "summary": "{{title}} - Your Answers\n\n", "questionSummary": "Question {{number}}: {{text}}\nYour answer: {{answer}}\n\n", "noAnswer": "No answer provided", "confirmQuestion": "Do you confirm your answers?", "error": "An error occurred while saving your answer. Please try again later.", "selected": "Your selection: ", "selectPrompt": "Select your answer:", "summaryTitle": "Your Answers for \"{{title}}\"", "questionLabel": "Question {{number}}:", "answerLabel": "Your answer:"}, "confirmation": {"noActiveSubmission": "You don't have a submission to confirm. Please use the /contests command to view active contests.", "contestNotActive": "This contest is no longer active. Please use the /contests command to view active contests.", "success": "Your answers for \"{{title}}\" have been successfully submitted!\n\nThank you for participating. Results will be announced when the contest ends.", "review": "You can review and change your answers. Let's start with the first question."}, "profile": {"title": "Your Profile", "username": "Username: {{username}}", "registrationDate": "Registration Date: {{date}}", "recentContests": "Last 5 Contests:", "contestItem": "{{index}}. {{title}} - {{status}}", "correctAnswers": "{{count}} correct answers", "winner": "💰Winner!", "noContests": "You haven't participated in any contests yet.", "notRegistered": "You need to register first. Please use the /start command."}, "help": {"title": "Sports Prediction Contest Bot Help", "commands": "Available commands:", "commandStart": "/start - Start the bot and register", "commandContests": "/contests - View active contests", "commandProfile": "/profile - View your profile and contest history", "commandHelp": "/help - Show this help message", "commandLanguage": "/language - Change language settings", "howTo": "How to participate in a contest:", "step1": "1. Use the /contests command to see current contests", "step2": "2. Select a contest you want to participate in", "step3": "3. Answer all questions in the contest", "step4": "4. Confirm your answers", "step5": "5. Wait for the contest results", "contact": "For any issues, please contact the administrator."}, "language": {"current": "Your current language: {{language}}", "select": "Please select a language:", "changed": "Your language has been changed to {{language}}."}, "winner": {"title": "Congratulations!", "message": "You won the \"{{title}}\" contest!\n\nYou answered {{correct}} out of {{total}} questions correctly.\n\n{{prizes}}\n\nThank you for participating!", "prizes": "Prize Information:"}, "buttons": {"yes": "Yes", "no": "No", "submit": "Yes, Submit", "review": "No, Review Answers"}, "errors": {"notRegistered": "You need to register first. Please use the /start command.", "general": "An error occurred. Please try again.", "userBlocked": "Your account has been blocked by the administrator. Please contact the administrator for more information."}, "contestDetails": {"startDate": "Start Date:", "endDate": "End Date:", "requirements": "Participation Requirements:", "prizes": "Prizes:", "channelRequirement": "Channel Requirement:", "channelMessage": "You need to be a member of {{channel}} channel", "questionCount": "Number of Questions:", "minCorrectAnswers": "Minimum Correct Answers Required to Win:"}, "status": {"active": "Active", "completed": "Completed", "draft": "Draft", "cancelled": "Cancelled"}, "notification": {"contestWinner": "🎉 Congratulations! You won the \"{{contestTitle}}\" contest! 🏆", "contestParticipation": "Thank you for your participation. Unfortunately, you didn't win this time. Good luck in the next contest!"}, "emojiGames": {"dice": {"totalValue": "🎲 You will make **{{count}}** dice rolls!\n\n🎯 **Target:** Total **{{target}}** or more points\n\n{{emoji}} Send {{emoji}} emoji to roll the dice!", "specificCount": "🎲 You will make **{{count}}** dice rolls!\n\n🎯 **Target:** At least **{{target}}** rolls of **{{value}}**\n\n{{emoji}} Send {{emoji}} emoji to roll the dice!"}, "basketball": "🏀 You will make **{{count}}** basketball shots!\n\n🎯 **Target:** At least **{{target}}** successful shots\n\n{{emoji}} Send {{emoji}} emoji to shoot!", "football": "⚽ You will make **{{count}}** penalty shots!\n\n🎯 **Target:** At least **{{target}}** goals\n\n{{emoji}} Send {{emoji}} emoji to shoot!", "dart": "🎯 You will make **{{count}}** dart throws!\n\n🎯 **Target:** At least **{{target}}** bullseyes\n\n{{emoji}} Send {{emoji}} emoji to throw!", "bowling": "🎳 You will make **{{count}}** bowling throws!\n\n🎯 **Target:** At least **{{target}}** strikes\n\n{{emoji}} Send {{emoji}} emoji to bowl!", "slot": "🎰 You have **{{count}}** slot machine spins!\n\n🎯 **Winning combinations:**\n{{combinations}}\n\n{{emoji}} Send {{emoji}} emoji to spin!", "start": "{{emoji}} **{{attempt}}/{{total}}** - Make your first attempt!", "nextAttempt": "{{emoji}} **{{attempt}}/{{total}}** - Make your next attempt!", "diceResult": "🎲 **{{attempt}}. Roll:** {{result}}", "basketballMiss": "🏀 **{{attempt}}. Shot:** Missed! 😔", "footballMiss": "⚽ **{{attempt}}. Shot:** Missed! 😔", "dartMiss": "🎯 **{{attempt}}. Throw:** {{result}} points - Missed!", "bowlingMiss": "🎳 **{{attempt}}. Throw:** Missed!", "slotResult": "🎰 **{{attempt}}. Spin:** Waiting for result...", "winner": "🎆🎇✨ **CONGRATULATIONS!** ✨🎇🎆\n\n🏆 You won the **{{title}}** contest!\n\n📊 **Your Score:** {{score}}", "notWinner": "😔 You didn't reach the target in **{{title}}** contest.\n\n📊 **Your Score:** {{score}}\n\nBetter luck next time! 🍀", "summary": "📋 **Summary:**\n🎯 Total attempts: {{attempts}}\n📊 Final score: {{score}}"}}}