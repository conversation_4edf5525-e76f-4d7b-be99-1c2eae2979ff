const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema(
  {
    telegramId: {
      type: String,
      required: true,
      unique: true,
    },
    username: {
      type: String,
      required: true,
    },
    tempUsername: {
      type: String,
    },
    firstName: {
      type: String,
    },
    lastName: {
      type: String,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    },
    blockReason: {
      type: String,
      default: '',
    },
    blockedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
    },
    blockedAt: {
      type: Date,
    },
    registrationDate: {
      type: Date,
      default: Date.now,
    },
    lastActivity: {
      type: Date,
      default: Date.now,
    },
    state: {
      type: String,
      enum: ['IDLE', 'REGISTERING', 'ANSWERING', 'CONFIRMING', 'PLAYING_EMOJI_GAME'],
      default: 'IDLE',
    },
    currentContest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Contest',
    },
    currentQuestionIndex: {
      type: Number,
      default: 0,
    },
    tempAnswers: {
      type: Map,
      of: String,
      default: {},
    },
    // For emoji games
    currentSubmission: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Submission',
    },
    language: {
      type: String,
      enum: ['tr', 'en', 'de', 'ar'],
      default: 'tr',
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model('User', UserSchema);
