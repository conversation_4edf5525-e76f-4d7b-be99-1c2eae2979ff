const Contest = require('../../models/Contest');
const Submission = require('../../models/Submission');
const { getTranslation } = require('../translations');
const { checkChannelMembership } = require('../utils');

// Emoji game constants
const EMOJI_GAMES = {
  DICE: {
    emoji: '🎲',
    values: [1, 2, 3, 4, 5, 6],
  },
  BASKETBALL: {
    emoji: '🏀',
    values: [1, 2, 3, 4, 5], // 1-4 miss, 5 success
  },
  FOOTBALL: {
    emoji: '⚽',
    values: [1, 2, 3, 4, 5], // 1-4 miss, 5 success
  },
  DART: {
    emoji: '🎯',
    values: [1, 2, 3, 4, 5, 6], // 6 is bullseye
  },
  BOWLING: {
    emoji: '🎳',
    values: [1, 2, 3, 4, 5, 6], // 6 is strike
  },
  SLOT: {
    emoji: '🎰',
    symbols: ['🍋', '🍇', '🍊', '7️⃣'], // lemon, grape, orange, seven
  },
};

// Success messages for each game
const SUCCESS_MESSAGES = {
  DICE: {
    tr: ['🎲 Harika atış!', '🎯 Mükemmel!', '🔥 Süper!'],
    en: ['🎲 Great roll!', '🎯 Perfect!', '🔥 Awesome!'],
    de: ['🎲 Großartiger Wurf!', '🎯 Perfekt!', '🔥 Super!'],
    ar: ['🎲 رمية رائعة!', '🎯 مثالي!', '🔥 رائع!'],
  },
  BASKETBALL: {
    tr: ['🏀 BASKETTTT! 🔥', '🎯 Tam onikiden!', '💪 Harika atış!'],
    en: ['🏀 BASKETTTT! 🔥', '🎯 Perfect shot!', '💪 Great shot!'],
    de: ['🏀 KORB! 🔥', '🎯 Perfekter Schuss!', '💪 Großartiger Schuss!'],
    ar: ['🏀 سلة! 🔥', '🎯 تسديدة مثالية!', '💪 تسديدة رائعة!'],
  },
  FOOTBALL: {
    tr: ['⚽ GOOOOOL! 🔥', '🎯 Tam köşeye!', '💪 Muhteşem gol!'],
    en: ['⚽ GOOOAAL! 🔥', '🎯 Perfect corner!', '💪 Amazing goal!'],
    de: ['⚽ TOOOOR! 🔥', '🎯 Perfekte Ecke!', '💪 Fantastisches Tor!'],
    ar: ['⚽ هدف! 🔥', '🎯 زاوية مثالية!', '💪 هدف رائع!'],
  },
  DART: {
    tr: ['🎯 TAM ONİKİDEN! 🔥', '💥 Bullseye!', '🎪 Mükemmel atış!'],
    en: ['🎯 BULLSEYE! 🔥', '💥 Perfect hit!', '🎪 Amazing throw!'],
    de: ['🎯 VOLLTREFFER! 🔥', '💥 Perfekter Treffer!', '🎪 Fantastischer Wurf!'],
    ar: ['🎯 في المركز! 🔥', '💥 إصابة مثالية!', '🎪 رمية رائعة!'],
  },
  BOWLING: {
    tr: ['🎳 STRIKE! 🔥', '💥 Hepsi devrildi!', '🎪 Mükemmel atış!'],
    en: ['🎳 STRIKE! 🔥', '💥 All pins down!', '🎪 Perfect throw!'],
    de: ['🎳 STRIKE! 🔥', '💥 Alle Pins umgeworfen!', '🎪 Perfekter Wurf!'],
    ar: ['🎳 سترايك! 🔥', '💥 سقطت كلها!', '🎪 رمية مثالية!'],
  },
  SLOT: {
    tr: ['🎰 JACKPOT! 🔥', '💰 Büyük kazanç!', '🎉 Tebrikler!'],
    en: ['🎰 JACKPOT! 🔥', '💰 Big win!', '🎉 Congratulations!'],
    de: ['🎰 JACKPOT! 🔥', '💰 Großer Gewinn!', '🎉 Glückwunsch!'],
    ar: ['🎰 جاكبوت! 🔥', '💰 ربح كبير!', '🎉 تهانينا!'],
  },
};

// Handle emoji game participation
const handleEmojiGameParticipation = async (bot, callbackQuery, user, contestId) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Get contest
    const contest = await Contest.findById(contestId);

    if (!contest || contest.status !== 'ACTIVE') {
      const translation = await getTranslation(user.language, 'bot.contests.notActive');
      return bot.sendMessage(chatId, translation);
    }

    // Check if contest is emoji game
    if (contest.type !== 'EMOJI_GAME') {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, translation);
    }

    // Check if contest has started and not ended
    const now = new Date();
    if (now < contest.startDate || now > contest.endDate) {
      const translation = await getTranslation(user.language, 'bot.contests.notActive');
      return bot.sendMessage(chatId, translation);
    }

    // Check channel membership if required
    if (contest.channelRequirement.required) {
      const isMember = await checkChannelMembership(bot, user.telegramId, contest.channelRequirement.channelUsername);
      if (!isMember) {
        const translation = await getTranslation(user.language, 'bot.contests.channelRequired', {
          channel: contest.channelRequirement.channelUsername,
        });
        return bot.sendMessage(chatId, translation);
      }
    }

    // Check if user already participated
    const existingSubmission = await Submission.findOne({
      user: user._id,
      contest: contestId,
    });

    if (existingSubmission) {
      const translation = await getTranslation(user.language, 'bot.contests.alreadyParticipated');
      return bot.sendMessage(chatId, translation);
    }

    // Update user state to waiting for game start
    user.state = 'WAITING_EMOJI_GAME_START';
    user.currentContest = contestId;
    await user.save();

    // Show contest details first
    const { formatContestDetails } = require('../utils');
    const contestDetails = await formatContestDetails(contest, user.language);

    await bot.sendMessage(chatId, contestDetails, {
      parse_mode: 'Markdown',
    });

    // Add a small delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Send start button
    const startButtonText = await getTranslation(user.language, 'bot.emojiGames.buttons.startContest');
    const keyboard = {
      inline_keyboard: [
        [
          {
            text: startButtonText,
            callback_data: `start_emoji_game_${contestId}`,
          },
        ],
      ],
    };

    const startPrompt = await getTranslation(user.language, 'bot.emojiGames.startPrompt') || '🎮 Yarışmaya başlamak için aşağıdaki butona tıklayın!';
    await bot.sendMessage(chatId, startPrompt, {
      reply_markup: keyboard,
    });

  } catch (error) {
    console.error('Error handling emoji game participation:', error);

    // Handle specific errors with appropriate messages
    let errorKey = 'bot.errors.general';

    if (error.message && error.message.includes('not found')) {
      errorKey = 'bot.errors.contestNotFound';
    } else if (error.message && error.message.includes('not active')) {
      errorKey = 'bot.errors.contestNotActive';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorKey = 'bot.errors.network';
    } else if (error.name === 'MongoServerError') {
      errorKey = 'bot.errors.database';
    }

    const translation = await getTranslation(user.language, errorKey);
    bot.sendMessage(chatId, `❌ ${translation}`);
  }
};

// Start emoji game
const startEmojiGame = async (bot, callbackQuery, user, contestId) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Get contest
    const contest = await Contest.findById(contestId);
    if (!contest || contest.status !== 'ACTIVE') {
      const translation = await getTranslation(user.language, 'bot.participation.notFound');
      return bot.sendMessage(chatId, translation);
    }

    // Check if user already has a submission for this contest
    const existingSubmission = await Submission.findOne({
      user: user._id,
      contest: contestId,
    });

    if (existingSubmission) {
      const translation = await getTranslation(user.language, 'bot.emojiGames.alreadyParticipated');
      return bot.sendMessage(chatId, translation);
    }

    // Create new submission
    const submission = await Submission.create({
      user: user._id,
      contest: contestId,
      emojiResults: {
        gameType: contest.emojiGame.gameType,
        attempts: [],
        totalScore: 0,
        achievedTarget: false,
      },
    });

    // Update user state
    user.state = 'PLAYING_EMOJI_GAME';
    user.currentContest = contestId;
    user.currentSubmission = submission._id;
    await user.save();

    // Send game instructions
    await sendGameInstructions(bot, chatId, user, contest);

  } catch (error) {
    console.error('Error starting emoji game:', error);

    // Handle specific MongoDB duplicate key error
    if (error.code === 11000 && error.keyPattern && error.keyPattern.user && error.keyPattern.contest) {
      const translation = await getTranslation(user.language, 'bot.emojiGames.alreadyParticipated');
      return bot.sendMessage(chatId, `⚠️ ${translation}`);
    }

    // Handle other errors
    const translation = await getTranslation(user.language, 'bot.errors.general');
    bot.sendMessage(chatId, `❌ ${translation}`);
  }
};

// Send game instructions
const sendGameInstructions = async (bot, chatId, user, contest) => {
  const gameType = contest.emojiGame.gameType;
  const attemptCount = contest.emojiGame.attemptCount;

  let instructions = '';
  let emoji = EMOJI_GAMES[gameType].emoji;

  // Get game-specific instructions
  switch (gameType) {
    case 'DICE':
      if (contest.emojiGame.diceSettings.targetType === 'TOTAL_VALUE') {
        instructions = await getTranslation(user.language, 'bot.emojiGames.dice.totalValue', {
          count: attemptCount,
          target: contest.emojiGame.diceSettings.targetValue,
          emoji: emoji,
        });
      } else {
        instructions = await getTranslation(user.language, 'bot.emojiGames.dice.specificCount', {
          count: attemptCount,
          target: contest.emojiGame.diceSettings.targetCount,
          value: contest.emojiGame.diceSettings.targetValue,
          emoji: emoji,
        });
      }
      break;
    case 'BASKETBALL':
      instructions = await getTranslation(user.language, 'bot.emojiGames.basketball', {
        count: attemptCount,
        target: contest.emojiGame.successTarget,
        emoji: emoji,
      });
      break;
    case 'FOOTBALL':
      instructions = await getTranslation(user.language, 'bot.emojiGames.football', {
        count: attemptCount,
        target: contest.emojiGame.successTarget,
        emoji: emoji,
      });
      break;
    case 'DART':
      instructions = await getTranslation(user.language, 'bot.emojiGames.dart', {
        count: attemptCount,
        target: contest.emojiGame.bullseyeTarget,
        emoji: emoji,
      });
      break;
    case 'BOWLING':
      instructions = await getTranslation(user.language, 'bot.emojiGames.bowling', {
        count: attemptCount,
        target: contest.emojiGame.strikeTarget,
        emoji: emoji,
      });
      break;
    case 'SLOT':
      const combinations = contest.emojiGame.slotSettings.winningCombinations
        .map(combo => `${combo.combination.join('')} - ${combo.name}`)
        .join('\n');
      instructions = await getTranslation(user.language, 'bot.emojiGames.slot', {
        count: attemptCount,
        combinations: combinations,
        emoji: emoji,
      });
      break;
  }

  // Send instructions
  await bot.sendMessage(chatId, instructions, { parse_mode: 'Markdown' });

  // Send first attempt prompt with button
  const startMessage = await getTranslation(user.language, 'bot.emojiGames.start', {
    emoji: emoji,
    attempt: 1,
    total: attemptCount,
  });

  // Get button text based on game type
  const buttonText = await getTranslation(user.language, `bot.emojiGames.buttons.${gameType.toLowerCase()}`);

  const keyboard = {
    inline_keyboard: [
      [
        {
          text: buttonText,
          callback_data: `play_emoji_${gameType.toLowerCase()}_${user.currentSubmission}`,
        },
      ],
    ],
  };

  await bot.sendMessage(chatId, startMessage, {
    reply_markup: keyboard,
    parse_mode: 'Markdown',
  });
};

// Handle emoji game play (button click)
const handleEmojiGamePlay = async (bot, callbackQuery, user, gameType, submissionId) => {
  const chatId = callbackQuery.message.chat.id;

  try {
    // Get submission
    const submission = await Submission.findById(submissionId);
    if (!submission) {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Get contest
    const contest = await Contest.findById(submission.contest);
    if (!contest) {
      const translation = await getTranslation(user.language, 'bot.errors.general');
      return bot.sendMessage(chatId, `❌ ${translation}`);
    }

    // Check if user has attempts left
    const currentAttempts = submission.emojiResults.attempts.length;
    const maxAttempts = contest.emojiGame.attemptCount;

    if (currentAttempts >= maxAttempts) {
      const translation = await getTranslation(user.language, 'bot.emojiGames.noAttemptsLeft') || 'Deneme hakkınız kalmadı!';
      return bot.sendMessage(chatId, translation);
    }

    // Send the actual emoji and simulate the game result
    let result;
    let success = false;
    let score = 0;
    let emojiToSend = '';

    switch (gameType.toUpperCase()) {
      case 'DICE':
        result = Math.floor(Math.random() * 6) + 1;
        score = result;
        emojiToSend = '🎲';

        // Check success based on target type
        if (contest.emojiGame.diceTargetType === 'TOTAL_VALUE') {
          const totalScore = submission.emojiResults.totalScore + score;
          success = totalScore >= contest.emojiGame.targetValue;
        } else if (contest.emojiGame.diceTargetType === 'SPECIFIC_COUNT') {
          const specificCount = submission.emojiResults.attempts.filter(a => a.result === contest.emojiGame.specificValue).length;
          if (result === contest.emojiGame.specificValue) {
            success = (specificCount + 1) >= contest.emojiGame.targetCount;
          }
        }
        break;

      case 'BASKETBALL':
        success = Math.random() < 0.4; // 40% success rate
        score = success ? 1 : 0;
        emojiToSend = '🏀';
        break;

      case 'FOOTBALL':
        success = Math.random() < 0.5; // 50% success rate
        score = success ? 1 : 0;
        emojiToSend = '⚽';
        break;

      case 'DART':
        const dartScore = Math.floor(Math.random() * 6) + 1;
        success = dartScore === 6; // Bullseye
        score = success ? 1 : 0;
        result = dartScore;
        emojiToSend = '🎯';
        break;

      case 'BOWLING':
        success = Math.random() < 0.3; // 30% success rate for strike
        score = success ? 1 : 0;
        emojiToSend = '🎳';
        break;

      case 'SLOT':
        // Simulate slot machine
        const symbols = ['🍒', '🔔', '➖', '7️⃣'];
        const slot1 = symbols[Math.floor(Math.random() * symbols.length)];
        const slot2 = symbols[Math.floor(Math.random() * symbols.length)];
        const slot3 = symbols[Math.floor(Math.random() * symbols.length)];
        result = `${slot1}${slot2}${slot3}`;
        emojiToSend = '🎰';

        // Check if it's a winning combination
        const winningCombos = contest.emojiGame.slotCombinations || [];
        success = winningCombos.includes(result);
        score = success ? 1 : 0;
        break;
    }

    // Send the emoji with game-specific animation
    await sendGameAnimation(bot, chatId, gameType.toUpperCase(), result, emojiToSend);

    // Add attempt to submission
    submission.emojiResults.attempts.push({
      result: result,
      success: success,
      timestamp: new Date(),
    });

    submission.emojiResults.totalScore += score;

    // Check if target achieved
    let targetAchieved = false;
    switch (gameType.toUpperCase()) {
      case 'DICE':
        if (contest.emojiGame.diceTargetType === 'TOTAL_VALUE') {
          targetAchieved = submission.emojiResults.totalScore >= contest.emojiGame.targetValue;
        } else if (contest.emojiGame.diceTargetType === 'SPECIFIC_COUNT') {
          const specificCount = submission.emojiResults.attempts.filter(a => a.result === contest.emojiGame.specificValue).length;
          targetAchieved = specificCount >= contest.emojiGame.targetCount;
        }
        break;

      case 'BASKETBALL':
      case 'FOOTBALL':
      case 'DART':
      case 'BOWLING':
        targetAchieved = submission.emojiResults.totalScore >= contest.emojiGame.successTarget;
        break;

      case 'SLOT':
        targetAchieved = success; // Any winning combination
        break;
    }

    submission.emojiResults.achievedTarget = targetAchieved;
    await submission.save();

    // Send result message
    const attemptNumber = submission.emojiResults.attempts.length;
    let resultMessage = '';

    switch (gameType.toUpperCase()) {
      case 'DICE':
        resultMessage = await getTranslation(user.language, 'bot.emojiGames.diceResult', {
          attempt: attemptNumber,
          result: result,
        });
        break;

      case 'BASKETBALL':
        if (success) {
          resultMessage = `🏀 **${attemptNumber}. Atış:** Başarılı! 🎉`;
        } else {
          resultMessage = await getTranslation(user.language, 'bot.emojiGames.basketballMiss', {
            attempt: attemptNumber,
          });
        }
        break;

      case 'FOOTBALL':
        if (success) {
          resultMessage = `⚽ **${attemptNumber}. Şut:** Gol! ⚽🎉`;
        } else {
          resultMessage = await getTranslation(user.language, 'bot.emojiGames.footballMiss', {
            attempt: attemptNumber,
          });
        }
        break;

      case 'DART':
        if (success) {
          resultMessage = `🎯 **${attemptNumber}. Atış:** Bullseye! 🎯🎉`;
        } else {
          resultMessage = await getTranslation(user.language, 'bot.emojiGames.dartMiss', {
            attempt: attemptNumber,
            result: result,
          });
        }
        break;

      case 'BOWLING':
        if (success) {
          resultMessage = `🎳 **${attemptNumber}. Atış:** Strike! 🎳🎉`;
        } else {
          resultMessage = await getTranslation(user.language, 'bot.emojiGames.bowlingMiss', {
            attempt: attemptNumber,
          });
        }
        break;

      case 'SLOT':
        resultMessage = `🎰 **${attemptNumber}. Çevirme:** ${result}`;
        if (success) {
          resultMessage += ' 🎉 **Kazandınız!**';
        }
        break;
    }

    await bot.sendMessage(chatId, resultMessage, { parse_mode: 'Markdown' });

    // Check if game is finished
    const hasAttemptsLeft = attemptNumber < maxAttempts;

    if (targetAchieved || !hasAttemptsLeft) {
      // Game finished
      user.state = 'IDLE';
      user.currentContest = null;
      user.currentSubmission = null;
      await user.save();

      // Send final result
      if (targetAchieved) {
        const winMessage = await getTranslation(user.language, 'bot.emojiGames.winner', {
          score: submission.emojiResults.totalScore,
        });
        await bot.sendMessage(chatId, winMessage, { parse_mode: 'Markdown' });
      } else {
        const loseMessage = await getTranslation(user.language, 'bot.emojiGames.notWinner', {
          title: contest.title,
          score: submission.emojiResults.totalScore,
        });
        await bot.sendMessage(chatId, loseMessage, { parse_mode: 'Markdown' });
      }

      // Send enhanced summary
      await sendGameSummary(bot, chatId, user, submission, gameType.toUpperCase());

    } else {
      // Continue game
      const gameEmoji = EMOJI_GAMES[gameType.toUpperCase()]?.emoji || '🎮';
      const nextMessage = await getTranslation(user.language, 'bot.emojiGames.nextAttempt', {
        emoji: gameEmoji,
        attempt: attemptNumber + 1,
        total: maxAttempts,
      });

      // Get button text
      const buttonText = await getTranslation(user.language, `bot.emojiGames.buttons.${gameType.toLowerCase()}`);

      const keyboard = {
        inline_keyboard: [
          [
            {
              text: buttonText,
              callback_data: `play_emoji_${gameType.toLowerCase()}_${submissionId}`,
            },
          ],
        ],
      };

      await bot.sendMessage(chatId, nextMessage, {
        reply_markup: keyboard,
        parse_mode: 'Markdown',
      });
    }

  } catch (error) {
    console.error('Error handling emoji game play:', error);

    // Handle specific errors with appropriate messages
    let errorKey = 'bot.errors.general';

    if (error.message && error.message.includes('not found')) {
      if (error.message.includes('submission')) {
        errorKey = 'bot.errors.submissionNotFound';
      } else {
        errorKey = 'bot.errors.contestNotFound';
      }
    } else if (error.message && error.message.includes('attempts')) {
      errorKey = 'bot.errors.noAttemptsLeft';
    } else if (error.name === 'MongoServerError') {
      errorKey = 'bot.errors.database';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorKey = 'bot.errors.network';
    }

    const translation = await getTranslation(user.language, errorKey);
    bot.sendMessage(chatId, `❌ ${translation}`);
  }
};

// Send game animation based on game type
const sendGameAnimation = async (bot, chatId, gameType, result, emojiToSend) => {
  switch (gameType) {
    case 'DICE':
      // Send dice emoji
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Send the result as a number
      await bot.sendMessage(chatId, `${result}`);
      break;

    case 'BASKETBALL':
      // Send basketball emoji
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 1200));
      break;

    case 'FOOTBALL':
      // Send football emoji
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 1200));
      break;

    case 'DART':
      // Send dart emoji
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Send the dart score
      await bot.sendMessage(chatId, `${result}`);
      break;

    case 'BOWLING':
      // Send bowling emoji
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 1300));
      break;

    case 'SLOT':
      // Send slot machine emoji
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Send spinning animation
      await bot.sendMessage(chatId, '🔄');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Send the result combination
      await bot.sendMessage(chatId, result);
      await new Promise(resolve => setTimeout(resolve, 500));
      break;

    default:
      // Default behavior
      await bot.sendMessage(chatId, emojiToSend);
      await new Promise(resolve => setTimeout(resolve, 1000));
  }
};

// Send enhanced game summary
const sendGameSummary = async (bot, chatId, user, submission, gameType) => {
  try {
    // Get game type name and action term
    const gameTypeName = await getTranslation(user.language, `bot.contestDetails.gameTypes.${gameType}`) || gameType;
    const actionTerm = await getTranslation(user.language, `bot.emojiGames.actionTerms.${gameType}`) || 'Deneme';

    // Build results string
    let results = '';
    const gameEmoji = EMOJI_GAMES[gameType]?.emoji || '🎮';

    submission.emojiResults.attempts.forEach((attempt, index) => {
      let resultDisplay = '';

      switch (gameType) {
        case 'DICE':
          resultDisplay = `${attempt.result}`;
          break;
        case 'BASKETBALL':
          resultDisplay = attempt.success ? '✅ Başarılı' : '❌ Kaçırdı';
          break;
        case 'FOOTBALL':
          resultDisplay = attempt.success ? '⚽ Gol!' : '❌ Kaçırdı';
          break;
        case 'DART':
          resultDisplay = attempt.success ? '🎯 Bullseye!' : `${attempt.result} puan`;
          break;
        case 'BOWLING':
          resultDisplay = attempt.success ? '🎳 Strike!' : '❌ Kaçırdı';
          break;
        case 'SLOT':
          resultDisplay = `${attempt.result}${attempt.success ? ' 🎉' : ''}`;
          break;
        default:
          resultDisplay = attempt.result;
      }

      results += `${gameEmoji} ${actionTerm} ${index + 1}: ${resultDisplay}\n`;
    });

    // Send summary message
    const summaryMessage = await getTranslation(user.language, 'bot.emojiGames.summary', {
      gameType: gameTypeName,
      results: results.trim(),
      score: submission.emojiResults.totalScore,
    });

    await bot.sendMessage(chatId, summaryMessage, { parse_mode: 'Markdown' });

  } catch (error) {
    console.error('Error sending game summary:', error);
    // Fallback to simple summary
    const fallbackMessage = `📊 Toplam Skorunuz: ${submission.emojiResults.totalScore}\n\n📝 Katıldığınız için teşekkür ederiz!`;
    await bot.sendMessage(chatId, fallbackMessage);
  }
};

module.exports = {
  handleEmojiGameParticipation,
  startEmojiGame,
  handleEmojiGamePlay,
  sendGameInstructions,
  sendGameAnimation,
  sendGameSummary,
  EMOJI_GAMES,
  SUCCESS_MESSAGES,
};
