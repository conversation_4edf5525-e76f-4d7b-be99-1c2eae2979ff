// Check if user is a member of the required channel
const checkChannelMembership = async (bot, userId, channelUsername) => {
  try {
    // If no channel requirement, return true
    if (!channelUsername) {
      return true;
    }

    // Format channel username to ensure it starts with @
    const formattedChannelUsername = channelUsername.startsWith('@')
      ? channelUsername
      : `@${channelUsername}`;

    // Get chat member status
    const chatMember = await bot.getChatMember(formattedChannelUsername, userId);

    // Check if user is a member
    return ['creator', 'administrator', 'member'].includes(chatMember.status);
  } catch (error) {
    console.error('Error checking channel membership:', error);
    return false;
  }
};

const { getTranslation } = require('./translations');

// Format contest details for display
const formatContestDetails = async (contest, language = 'tr') => {
  let message = `🏆 *${contest.title}* 🏆\n\n`;
  message += `📝 ${contest.description}\n\n`;

  // Add dates with time
  const startDateFormat = language === 'tr' ? 'tr-TR' : language === 'de' ? 'de-DE' : language === 'ar' ? 'ar-SA' : 'en-US';
  const endDateFormat = startDateFormat;

  // Format date and time
  const formatDateTime = (date, locale) => {
    const dateObj = new Date(date);
    const dateStr = dateObj.toLocaleDateString(locale);

    // Format time as HH:MM
    const hours = dateObj.getHours().toString().padStart(2, '0');
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');
    const timeStr = `${hours}:${minutes}`;

    return `${dateStr} ${timeStr}`;
  };

  // Get translations for labels
  const startDateLabel = await getTranslation(language, 'bot.contestDetails.startDate');
  const endDateLabel = await getTranslation(language, 'bot.contestDetails.endDate');
  const requirementsLabel = await getTranslation(language, 'bot.contestDetails.requirements');
  const prizesLabel = await getTranslation(language, 'bot.contestDetails.prizes');
  const channelLabel = await getTranslation(language, 'bot.contestDetails.channelRequirement');
  const questionsLabel = await getTranslation(language, 'bot.contestDetails.questionCount');
  const minCorrectLabel = await getTranslation(language, 'bot.contestDetails.minCorrectAnswers');

  message += `🕒 *${startDateLabel}* ${formatDateTime(contest.startDate, startDateFormat)}\n`;
  message += `⏰ *${endDateLabel}* ${formatDateTime(contest.endDate, endDateFormat)}\n\n`;

  // Add requirements if any
  if (contest.requirements) {
    message += `📋 *${requirementsLabel}*\n${contest.requirements}\n\n`;
  }

  // Add prizes if any
  if (contest.prizes) {
    message += `🎁 *${prizesLabel}*\n${contest.prizes}\n\n`;
  }

  // Add channel requirement if any
  if (contest.channelRequirement && contest.channelRequirement.required) {
    const channelMessage = await getTranslation(language, 'bot.contestDetails.channelMessage', { channel: contest.channelRequirement.channelUsername });
    message += `📢 *${channelLabel}* ${channelMessage}\n\n`;
  }

  // Add contest-specific details
  if (contest.type === 'EMOJI_GAME') {
    // Emoji game details
    const gameTypeNames = {
      'DICE': '🎲',
      'BASKETBALL': '🏀',
      'FOOTBALL': '⚽',
      'DART': '🎯',
      'BOWLING': '🎳',
      'SLOT': '🎰'
    };
    const gameEmoji = gameTypeNames[contest.emojiGame.gameType] || '🎮';
    const gameTypeLabel = await getTranslation(language, 'bot.contestDetails.gameType') || 'Oyun Tipi';
    const attemptCountLabel = await getTranslation(language, 'bot.contestDetails.attemptCount') || 'Deneme Sayısı';

    message += `${gameEmoji} *${gameTypeLabel}* ${contest.emojiGame.gameType}\n`;
    message += `🎯 *${attemptCountLabel}* ${contest.emojiGame.attemptCount}\n\n`;
  } else {
    // Sports prediction details
    message += `❓ *${questionsLabel}* ${contest.questions.length}\n`;
    message += `✅ *${minCorrectLabel}* ${contest.minCorrectAnswers}\n\n`;
  }

  message += `🍀 ${language === 'tr' ? 'İyi şanslar!' : language === 'en' ? 'Good luck!' : language === 'de' ? 'Viel Glück!' : 'حظا سعيدا!'}`;

  return message;
};

// Create keyboard for contest list
const createContestListKeyboard = async (contests, language = 'tr') => {
  const keyboard = [];

  // Get translation for active label if needed
  const activeLabel = await getTranslation(language, 'bot.contests.active');

  for (const contest of contests) {
    // Format the contest title with status indicator if needed
    let titleText = contest.title;

    // Add status indicator based on contest status
    if (contest.status === 'ACTIVE') {
      titleText = `${titleText} [${activeLabel}]`;
    }

    keyboard.push([
      {
        text: titleText,
        callback_data: `contest_${contest._id}`,
      },
    ]);
  }

  return {
    inline_keyboard: keyboard,
  };
};

// Create keyboard for question options
const createOptionsKeyboard = async (question) => {
  const keyboard = [];

  for (const option of question.options) {
    keyboard.push([
      {
        text: option.text,
        callback_data: `answer_${question._id}_${option.value}`,
      },
    ]);
  }

  return {
    inline_keyboard: keyboard,
  };
};

// Create confirmation keyboard
const createConfirmationKeyboard = async (language = 'tr') => {
  // Get translations for buttons
  const submitText = await getTranslation(language, 'bot.buttons.submit');
  const reviewText = await getTranslation(language, 'bot.buttons.review');

  return {
    inline_keyboard: [
      [
        { text: submitText, callback_data: 'confirm_answers_yes' },
        { text: reviewText, callback_data: 'confirm_answers_no' },
      ],
    ],
  };
};

module.exports = {
  checkChannelMembership,
  formatContestDetails,
  createContestListKeyboard,
  createOptionsKeyboard,
  createConfirmationKeyboard,
};
