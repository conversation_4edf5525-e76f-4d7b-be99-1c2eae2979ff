const TelegramBot = require('node-telegram-bot-api');
const BotSettings = require('../models/BotSettings');
const User = require('../models/User');
const Contest = require('../models/Contest');
const Submission = require('../models/Submission');
const { checkChannelMembership } = require('./utils');
const { handleStart } = require('./handlers/start');
const { handleRegistration } = require('./handlers/registration');
const { handleContestList } = require('./handlers/contestList');
const { handleContestParticipation } = require('./handlers/contestParticipation');
const { handleAnswering } = require('./handlers/answering');
const { handleConfirmation } = require('./handlers/confirmation');
const { handleLanguage } = require('./handlers/language');
const { handleEmojiGameParticipation, startEmojiGame, handleEmojiGamePlay } = require('./handlers/emojiGames');
const { handleEmojiGameResult } = require('./handlers/emojiGameResults');
const { getLocalizedMessage } = require('./utils/i18n');
const { getTranslation } = require('./translations');

let bot;

// Initialize the bot
const initBot = async () => {
  try {
    // Get bot settings
    const settings = await BotSettings.getSettings();

    // Check if token is provided
    if (!process.env.TELEGRAM_BOT_TOKEN || process.env.TELEGRAM_BOT_TOKEN === 'your_telegram_bot_token_here') {
      console.warn('Telegram bot token not provided or is default. Bot will not be fully functional.');
      // Create a mock bot for testing
      bot = {
        sendMessage: (chatId, message, options) => {
          console.log(`[MOCK BOT] Sending message to ${chatId}: ${message}`);
          return Promise.resolve({ message_id: Date.now() });
        },
        on: () => { },
        onText: () => { },
        setMyCommands: () => Promise.resolve(true)
      };
    } else {
      // Create a real bot instance
      bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });

      // Set bot commands
      await setCommands();

      // Set up message handlers
      setupMessageHandlers();
    }

    console.log('Telegram bot initialized');
    return bot;
  } catch (error) {
    console.error('Error initializing bot:', error);
    // Create a mock bot in case of error
    bot = {
      sendMessage: (chatId, message, options) => {
        console.log(`[MOCK BOT] Sending message to ${chatId}: ${message}`);
        return Promise.resolve({ message_id: Date.now() });
      },
      on: () => { },
      onText: () => { },
      setMyCommands: () => Promise.resolve(true)
    };
    return bot;
  }
};

// Set bot commands
const setCommands = async () => {
  try {
    // Define commands for different languages
    const commandSets = {
      tr: [
        { command: 'start', description: '🚀 Botu başlat ve kayıt ol' },
        { command: 'contests', description: '🏆 Aktif yarışmaları görüntüle' },
        { command: 'yarismalar', description: '🏆 Aktif yarışmaları görüntüle' },
        { command: 'profile', description: '👤 Profilini görüntüle' },
        { command: 'profil', description: '👤 Profilini görüntüle' },
        { command: 'language', description: '🌍 Dil ayarlarını değiştir' },
        { command: 'dil', description: '🌍 Dil ayarlarını değiştir' },
        { command: 'help', description: '❓ Yardım mesajını görüntüle' },
        { command: 'yardim', description: '❓ Yardım mesajını görüntüle' },
      ],
      en: [
        { command: 'start', description: '🚀 Start the bot and register' },
        { command: 'contests', description: '🏆 View active contests' },
        { command: 'profile', description: '👤 View your profile' },
        { command: 'language', description: '🌍 Change language settings' },
        { command: 'help', description: '❓ Show help message' },
      ],
      de: [
        { command: 'start', description: '🚀 Starten Sie den Bot und registrieren Sie sich' },
        { command: 'contests', description: '🏆 Aktive Wettbewerbe anzeigen' },
        { command: 'wettbewerbe', description: '🏆 Aktive Wettbewerbe anzeigen' },
        { command: 'profile', description: '👤 Ihr Profil anzeigen' },
        { command: 'profil', description: '👤 Ihr Profil anzeigen' },
        { command: 'language', description: '🌍 Spracheinstellungen ändern' },
        { command: 'sprache', description: '🌍 Spracheinstellungen ändern' },
        { command: 'help', description: '❓ Hilfe anzeigen' },
        { command: 'hilfe', description: '❓ Hilfe anzeigen' },
      ],
      ar: [
        { command: 'start', description: '🚀 بدء البوت والتسجيل' },
        { command: 'contests', description: '🏆 عرض المسابقات النشطة' },
        { command: 'profile', description: '👤 عرض ملفك الشخصي' },
        { command: 'language', description: '🌍 تغيير إعدادات اللغة' },
        { command: 'help', description: '❓ عرض رسالة المساعدة' },
      ],
    };

    // Set default commands (Turkish)
    try {
      await bot.setMyCommands(commandSets.tr);
      console.log('Default commands set successfully');
    } catch (error) {
      console.error('Error setting default commands:', error.message);
    }

    // Set commands for specific languages
    try {
      await bot.setMyCommands(commandSets.tr, { language_code: 'tr' });
      console.log('Turkish commands set successfully');
    } catch (error) {
      console.error('Error setting Turkish commands:', error.message);
    }

    try {
      await bot.setMyCommands(commandSets.en, { language_code: 'en' });
      console.log('English commands set successfully');
    } catch (error) {
      console.error('Error setting English commands:', error.message);
    }

    try {
      await bot.setMyCommands(commandSets.de, { language_code: 'de' });
      console.log('German commands set successfully');
    } catch (error) {
      console.error('Error setting German commands:', error.message);
    }

    try {
      await bot.setMyCommands(commandSets.ar, { language_code: 'ar' });
      console.log('Arabic commands set successfully');
    } catch (error) {
      console.error('Error setting Arabic commands:', error.message);
    }

    console.log('Bot commands setup completed');
  } catch (error) {
    console.error('Error setting bot commands:', error);
  }
};

// Set up message handlers
const setupMessageHandlers = () => {
  // Handle /start command
  bot.onText(/\/start/, async (msg) => {
    try {
      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.sendMessage(chatId, settings.maintenanceMessage);
      }

      // Get user
      const user = await User.findOne({ telegramId: msg.from.id.toString() });

      // Check if user is blocked
      if (user && user.isBlocked) {
        const blockedMessage = await getTranslation(user.language, 'bot.errors.userBlocked');
        return bot.sendMessage(chatId, blockedMessage);
      }

      // If user is in registration process, don't allow commands
      if (user && user.state === 'REGISTERING') {
        const registerMessage = await getTranslation(user.language, 'bot.registration.askAgain');
        return bot.sendMessage(chatId, registerMessage);
      }

      // Handle start command
      await handleStart(bot, msg);
    } catch (error) {
      console.error('Error handling /start command:', error);
    }
  });

  // Handle /contests command (and localized versions)
  bot.onText(/\/(contests|yarismalar|wettbewerbe)/, async (msg) => {
    try {
      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.sendMessage(chatId, settings.maintenanceMessage);
      }

      // Get user
      const user = await User.findOne({ telegramId: msg.from.id.toString() });

      if (!user) {
        return bot.sendMessage(
          chatId,
          'Önce kayıt olmanız gerekmektedir. Lütfen /start komutunu kullanın.'
        );
      }

      // If user is in registration process, don't allow commands
      if (user.state === 'REGISTERING') {
        const registerMessage = await getTranslation(user.language, 'bot.registration.askAgain');
        return bot.sendMessage(chatId, registerMessage);
      }

      // Handle contest list
      await handleContestList(bot, msg, user);
    } catch (error) {
      console.error('Error handling /contests command:', error);
    }
  });

  // Handle /profile command (and localized versions)
  bot.onText(/\/(profile|profil)/, async (msg) => {
    try {
      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.sendMessage(chatId, settings.maintenanceMessage);
      }

      // Get user
      const user = await User.findOne({ telegramId: msg.from.id.toString() });

      if (!user) {
        return bot.sendMessage(
          chatId,
          'Önce kayıt olmanız gerekmektedir. Lütfen /start komutunu kullanın.'
        );
      }

      // If user is in registration process, don't allow commands
      if (user.state === 'REGISTERING') {
        const registerMessage = await getTranslation(user.language, 'bot.registration.askAgain');
        return bot.sendMessage(chatId, registerMessage);
      }

      // Get user's submissions
      const submissions = await Submission.find({ user: user._id })
        .populate('contest', 'title status')
        .sort({ submittedAt: -1 })
        .limit(5);

      // Get localized messages
      const profileMessages = {
        tr: `👤 *Profiliniz*\n\n`,
        en: `*Your Profile*\n\n`,
        de: `*Ihr Profil*\n\n`,
        ar: `*ملفك الشخصي*\n\n`
      };

      const usernameMessages = {
        tr: `Kullanıcı Adı: ${user.username}\n`,
        en: `Username: ${user.username}\n`,
        de: `Benutzername: ${user.username}\n`,
        ar: `اسم المستخدم: ${user.username}\n`
      };

      const dateFormat = user.language === 'en' ? 'en-US' : user.language === 'de' ? 'de-DE' : user.language === 'ar' ? 'ar-SA' : 'tr-TR';
      const registrationDateMessages = {
        tr: `Kayıt Tarihi: ${new Date(user.registrationDate).toLocaleDateString(dateFormat)}\n\n`,
        en: `Registration Date: ${new Date(user.registrationDate).toLocaleDateString(dateFormat)}\n\n`,
        de: `Registrierungsdatum: ${new Date(user.registrationDate).toLocaleDateString(dateFormat)}\n\n`,
        ar: `تاريخ التسجيل: ${new Date(user.registrationDate).toLocaleDateString(dateFormat)}\n\n`
      };

      let message = getLocalizedMessage(user, profileMessages);
      message += getLocalizedMessage(user, usernameMessages);
      message += getLocalizedMessage(user, registrationDateMessages);

      // Prepare inline keyboard for viewing answers
      const inlineKeyboard = [];

      if (submissions.length > 0) {
        const recentContestsMessages = {
          tr: `🏆 *Son 5 Yarışma:*\n`,
          en: `*Recent Contests:*\n`,
          de: `*Neueste Wettbewerbe:*\n`,
          ar: `*المسابقات الأخيرة:*\n`
        };
        message += getLocalizedMessage(user, recentContestsMessages);

        submissions.forEach((submission, index) => {
          message += `${index + 1}. ${submission.contest.title} - `;

          if (submission.contest.status === 'COMPLETED') {
            const correctAnswersMessages = {
              tr: `${submission.correctAnswers} doğru cevap`,
              en: `${submission.correctAnswers} correct answers`,
              de: `${submission.correctAnswers} richtige Antworten`,
              ar: `${submission.correctAnswers} إجابات صحيحة`
            };
            message += getLocalizedMessage(user, correctAnswersMessages);

            if (submission.isWinner) {
              const winnerMessages = {
                tr: ` (💰Ödül Kazandınız!)`,
                en: ` (Winner!)`,
                de: ` (Gewinner!)`,
                ar: ` (فائز!)`
              };
              message += getLocalizedMessage(user, winnerMessages);
            }

            // Add button for completed contests to view answers
            const viewAnswersText = {
              tr: `${index + 1}. ${submission.contest.title} - Cevaplarımı Gör`,
              en: `${index + 1}. ${submission.contest.title} - View My Answers`,
              de: `${index + 1}. ${submission.contest.title} - Meine Antworten anzeigen`,
              ar: `${index + 1}. ${submission.contest.title} - عرض إجاباتي`
            };

            inlineKeyboard.push([{
              text: getLocalizedMessage(user, viewAnswersText),
              callback_data: `view_answers_${submission._id}`
            }]);
          } else {
            message += `${submission.contest.status}`;
          }

          message += `\n`;
        });
      } else {
        const noContestsMessages = {
          tr: `Henüz hiçbir yarışmaya katılmadınız.`,
          en: `You haven't participated in any contests yet.`,
          de: `Sie haben noch an keinen Wettbewerben teilgenommen.`,
          ar: `لم تشارك في أي مسابقات حتى الآن.`
        };
        message += getLocalizedMessage(user, noContestsMessages);
      }

      // Send message with inline keyboard if there are completed contests
      const options = {
        parse_mode: 'Markdown',
        ...(inlineKeyboard.length > 0 && {
          reply_markup: {
            inline_keyboard: inlineKeyboard
          }
        })
      };

      bot.sendMessage(chatId, message, options);
    } catch (error) {
      console.error('Error handling /profile command:', error);
    }
  });

  // Handle /language command (and localized versions)
  bot.onText(/\/(language|dil|sprache)/, async (msg) => {
    try {
      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.sendMessage(chatId, settings.maintenanceMessage);
      }

      // Get user
      const user = await User.findOne({ telegramId: msg.from.id.toString() });

      if (!user) {
        return bot.sendMessage(
          chatId,
          'Önce kayıt olmanız gerekmektedir. Lütfen /start komutunu kullanın.'
        );
      }

      // If user is in registration process, don't allow commands
      if (user.state === 'REGISTERING') {
        const registerMessage = await getTranslation(user.language, 'bot.registration.askAgain');
        return bot.sendMessage(chatId, registerMessage);
      }

      // Handle language command
      await handleLanguage(bot, msg, user);
    } catch (error) {
      console.error('Error handling /language command:', error);
    }
  });

  // Handle /help command (and localized versions)
  bot.onText(/\/(help|yardim|hilfe)/, async (msg) => {
    try {
      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.sendMessage(chatId, settings.maintenanceMessage);
      }

      // Get user
      const user = await User.findOne({ telegramId: msg.from.id.toString() });

      // If user is in registration process, don't allow commands
      if (user && user.state === 'REGISTERING') {
        const registerMessage = await getTranslation(user.language, 'bot.registration.askAgain');
        return bot.sendMessage(chatId, registerMessage);
      }

      // Get help message based on user language
      let helpMessage = '';

      if (!user || user.language === 'tr') {
        helpMessage = `
💰 *Spor Tahmin Yarışması Bot Yardımı* 💰

📋 *Kullanılabilir komutlar:*
🚀 /start - Botu başlat ve kayıt ol
🏆 /contests (veya /yarismalar) - Aktif yarışmaları görüntüle
👤 /profile (veya /profil) - Profilinizi ve yarışma geçmişinizi görüntüleyin
❓ /help (veya /yardim) - Bu yardım mesajını göster
🌍 /language (veya /dil) - Dil ayarlarını değiştir

📝 *Bir yarışmaya nasıl katılınır:*
1️⃣ Mevcut yarışmaları görmek için /contests komutunu kullanın
2️⃣ Katılmak istediğiniz bir yarışma seçin
3️⃣ Yarışmadaki tüm soruları cevaplayın
4️⃣ Cevaplarınızı onaylayın
5️⃣ Yarışma sonuçlarını bekleyin

📞 Herhangi bir sorun için lütfen yönetici ile iletişime geçin.
        `;
      } else if (user.language === 'en') {
        helpMessage = `
💰 *Sports Prediction Contest Bot Help* 💰

📋 *Available commands:*
🚀 /start - Start the bot and register
🏆 /contests - View active contests
👤 /profile - View your profile and contest history
❓ /help - Show this help message
🌍 /language - Change language settings

📝 *How to participate in a contest:*
1️⃣ Use /contests to see available contests
2️⃣ Select a contest to participate
3️⃣ Answer all questions in the contest
4️⃣ Confirm your answers
5️⃣ Wait for the contest results

📞 For any issues, please contact the administrator.
        `;
      } else if (user.language === 'de') {
        helpMessage = `
💰 *Sportvorhersage-Wettbewerb Bot-Hilfe* 💰

📋 *Verfügbare Befehle:*
🚀 /start - Starten Sie den Bot und registrieren Sie sich
🏆 /contests (oder /wettbewerbe) - Aktive Wettbewerbe anzeigen
👤 /profile (oder /profil) - Ihr Profil und Ihre Wettbewerbshistorie anzeigen
❓ /help (oder /hilfe) - Diese Hilfemeldung anzeigen
🌍 /language (oder /sprache) - Spracheinstellungen ändern

📝 *Wie man an einem Wettbewerb teilnimmt:*
1️⃣ Verwenden Sie /contests, um verfügbare Wettbewerbe zu sehen
2️⃣ Wählen Sie einen Wettbewerb zur Teilnahme aus
3️⃣ Beantworten Sie alle Fragen im Wettbewerb
4️⃣ Bestätigen Sie Ihre Antworten
5️⃣ Warten Sie auf die Wettbewerbsergebnisse

📞 Bei Problemen wenden Sie sich bitte an den Administrator.
        `;
      } else if (user.language === 'ar') {
        helpMessage = `
💰 *مساعدة بوت مسابقة التنبؤ الرياضي* 💰

📋 *الأوامر المتاحة:*
🚀 /start - بدء البوت والتسجيل
🏆 /contests (أو /مسابقات) - عرض المسابقات النشطة
👤 /profile (أو /الملف) - عرض ملفك الشخصي وتاريخ المسابقات
❓ /help (أو /مساعدة) - عرض رسالة المساعدة هذه
🌍 /language (أو /لغة) - تغيير إعدادات اللغة

📝 *كيفية المشاركة في مسابقة:*
1️⃣ استخدم /contests لرؤية المسابقات المتاحة
2️⃣ اختر مسابقة للمشاركة
3️⃣ أجب على جميع الأسئلة في المسابقة
4️⃣ أكد إجاباتك
5️⃣ انتظر نتائج المسابقة

📞 لأي مشاكل، يرجى الاتصال بالمسؤول.
        `;
      }

      bot.sendMessage(chatId, helpMessage, { parse_mode: 'Markdown' });
    } catch (error) {
      console.error('Error handling /help command:', error);
    }
  });

  // Handle callback queries
  bot.on('callback_query', async (callbackQuery) => {
    try {
      const action = callbackQuery.data;
      const msg = callbackQuery.message;
      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.answerCallbackQuery(
          callbackQuery.id,
          { text: settings.maintenanceMessage }
        );
      }

      // Get user
      const user = await User.findOne({ telegramId: callbackQuery.from.id.toString() });

      if (!user) {
        bot.answerCallbackQuery(callbackQuery.id, {
          text: 'Önce kayıt olmanız gerekmektedir. Lütfen /start komutunu kullanın.',
          show_alert: true,
        });
        return;
      }

      // Check if user is blocked
      if (user.isBlocked) {
        bot.answerCallbackQuery(callbackQuery.id, {
          text: await getTranslation(user.language, 'bot.errors.userBlocked'),
          show_alert: true,
        });
        return;
      }

      // Handle different callback actions
      if (action === 'confirm_username_yes') {
        // Handle username confirmation
        await handleRegistration(bot, callbackQuery, user, true);
      } else if (action === 'confirm_username_no') {
        // Handle username rejection
        await handleRegistration(bot, callbackQuery, user, false);
      } else if (action.startsWith('contest_')) {
        // Handle contest selection
        const contestId = action.split('_')[1];

        // Check contest type and route accordingly
        const contest = await Contest.findById(contestId);
        if (contest && contest.type === 'EMOJI_GAME') {
          await handleEmojiGameParticipation(bot, callbackQuery, user, contestId);
        } else {
          await handleContestParticipation(bot, callbackQuery, user, contestId);
        }
      } else if (action.startsWith('answer_')) {
        // Handle answer selection
        const [, questionId, optionValue] = action.split('_');
        await handleAnswering(bot, callbackQuery, user, questionId, optionValue);
      } else if (action === 'confirm_answers_yes') {
        // Handle answers confirmation
        await handleConfirmation(bot, callbackQuery, user, true);
      } else if (action === 'confirm_answers_no') {
        // Handle answers rejection
        await handleConfirmation(bot, callbackQuery, user, false);
      } else if (action.startsWith('language_')) {
        // Handle language selection
        const language = action.split('_')[1];
        await handleLanguage(bot, callbackQuery.message, user, language);
      } else if (action.startsWith('view_answers_')) {
        // Handle viewing user's answers
        const submissionId = action.split('_')[2];
        await handleViewAnswers(bot, callbackQuery, user, submissionId);
      } else if (action.startsWith('start_emoji_game_')) {
        // Handle emoji game start
        const contestId = action.split('_')[3];
        await startEmojiGame(bot, callbackQuery, user, contestId);
      } else if (action.startsWith('play_emoji_')) {
        // Handle emoji game play
        const gameType = action.split('_')[2];
        const submissionId = action.split('_')[3];
        await handleEmojiGamePlay(bot, callbackQuery, user, gameType, submissionId);
      }

      // Answer callback query to remove loading state
      bot.answerCallbackQuery(callbackQuery.id);
    } catch (error) {
      console.error('Error handling callback query:', error);
      bot.answerCallbackQuery(callbackQuery.id, {
        text: 'Bir hata oluştu. Lütfen tekrar deneyin.',
        show_alert: true,
      });
    }
  });

  // Handle text messages (for registration)
  bot.on('message', async (msg) => {
    try {
      // Ignore commands
      if (msg.text && msg.text.startsWith('/')) {
        return;
      }

      const chatId = msg.chat.id;
      const settings = await BotSettings.getSettings();

      // Check if bot is in maintenance mode
      if (settings.maintenanceMode) {
        return bot.sendMessage(chatId, settings.maintenanceMessage);
      }

      // Get user
      const user = await User.findOne({ telegramId: msg.from.id.toString() });

      if (!user) {
        return bot.sendMessage(
          chatId,
          'Önce kayıt olmanız gerekmektedir. Lütfen /start komutunu kullanın.'
        );
      }

      // Check if user is blocked
      if (user.isBlocked) {
        return bot.sendMessage(
          chatId,
          await getTranslation(user.language, 'bot.errors.userBlocked')
        );
      }

      // Handle user state
      if (user.state === 'REGISTERING' && msg.text) {
        // Check if the message is a command
        if (msg.text.startsWith('/')) {
          // Ignore commands during registration
          const registerMessage = await getTranslation(user.language, 'bot.registration.askAgain');
          return bot.sendMessage(chatId, registerMessage);
        }

        // Store the username temporarily without changing state
        user.tempUsername = msg.text.trim();
        await user.save();

        // Ask for confirmation
        const confirmMessage = await getTranslation(user.language, 'bot.registration.confirm', { username: user.tempUsername });

        // Get button translations
        const yesText = await getTranslation(user.language, 'bot.buttons.yes');
        const noText = await getTranslation(user.language, 'bot.buttons.no');

        const confirmKeyboard = {
          inline_keyboard: [
            [
              { text: yesText, callback_data: 'confirm_username_yes' },
              { text: noText, callback_data: 'confirm_username_no' },
            ],
          ],
        };

        bot.sendMessage(
          chatId,
          confirmMessage,
          { reply_markup: confirmKeyboard }
        );
      } else if (user.state === 'PLAYING_EMOJI_GAME') {
        // Handle emoji game results
        await handleEmojiGameResult(bot, msg, user);
      }
    } catch (error) {
      console.error('Error handling message:', error);
    }
  });
};

// Notify winners of a completed contest
const notifyWinners = async (contestId) => {
  try {
    const contest = await Contest.findById(contestId);
    if (!contest || contest.status !== 'COMPLETED') {
      return;
    }

    // Get all winning submissions
    const winningSubmissions = await Submission.find({
      contest: contestId,
      isWinner: true,
    }).populate('user');

    // Send notification to each winner
    for (const submission of winningSubmissions) {
      const user = submission.user;

      // Get localized messages based on user language
      let message = '';

      if (!user.language || user.language === 'tr') {
        message = `
🎉 *Tebrikler!* 🎉

"${contest.title}" yarışmasında kazandınız!

${contest.questions.length} sorudan ${submission.correctAnswers} doğru cevap verdiniz.

${contest.prizes ? `*Ödül Bilgisi:*\n${contest.prizes}` : ''}

Katıldığınız için teşekkür ederiz!
        `;
      } else if (user.language === 'en') {
        message = `
🎉 *Congratulations!* 🎉

You are a winner in the contest "${contest.title}"!

You got ${submission.correctAnswers} correct answers out of ${contest.questions.length}.

${contest.prizes ? `*Prize Information:*\n${contest.prizes}` : ''}

Thank you for participating!
        `;
      } else if (user.language === 'de') {
        message = `
🎉 *Herzlichen Glückwunsch!* 🎉

Sie sind ein Gewinner im Wettbewerb "${contest.title}"!

Sie haben ${submission.correctAnswers} richtige Antworten von ${contest.questions.length} erreicht.

${contest.prizes ? `*Preisinformationen:*\n${contest.prizes}` : ''}

Vielen Dank für Ihre Teilnahme!
        `;
      } else if (user.language === 'ar') {
        message = `
🎉 *تهانينا!* 🎉

أنت فائز في المسابقة "${contest.title}"!

لقد حصلت على ${submission.correctAnswers} إجابات صحيحة من أصل ${contest.questions.length}.

${contest.prizes ? `*معلومات الجائزة:*\n${contest.prizes}` : ''}

شكراً لمشاركتك!
        `;
      }

      try {
        await bot.sendMessage(user.telegramId, message, { parse_mode: 'Markdown' });
      } catch (error) {
        console.error(`Error sending winner notification to user ${user._id}:`, error);
      }
    }
  } catch (error) {
    console.error('Error notifying winners:', error);
  }
};

// Start the bot
const startBot = async () => {
  if (!bot) {
    await initBot();
  }
  return bot;
};

// Get the bot instance
const getBot = () => {
  return bot;
};

/**
 * Kullanıcıya bildirim gönderme
 */
const sendNotificationToUser = async (userId, message, options = {}) => {
  try {
    if (!bot) {
      throw new Error('Bot başlatılmadı');
    }

    const user = await User.findById(userId);
    if (!user || !user.telegramId) {
      throw new Error('Kullanıcı bulunamadı veya Telegram ID yok');
    }

    const result = await bot.sendMessage(user.telegramId, message, {
      parse_mode: 'Markdown',
      ...options
    });

    return result;
  } catch (error) {
    console.error(`Kullanıcıya bildirim gönderme hatası: ${error.message}`);
    throw error;
  }
};

/**
 * Tüm kullanıcılara bildirim gönderme
 */
const sendBroadcastNotification = async (message, options = {}) => {
  try {
    if (!bot) {
      throw new Error('Bot başlatılmadı');
    }

    const users = await User.find({ isAdmin: false });
    const results = {
      total: users.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const user of users) {
      try {
        if (!user.telegramId) continue;

        await bot.sendMessage(user.telegramId, message, {
          parse_mode: 'Markdown',
          ...options
        });

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          userId: user._id,
          telegramId: user.telegramId,
          error: error.message
        });
      }
    }

    return results;
  } catch (error) {
    console.error(`Toplu bildirim gönderme hatası: ${error.message}`);
    throw error;
  }
};

/**
 * Kullanıcının yarışma cevaplarını görüntüleme
 */
const handleViewAnswers = async (bot, callbackQuery, user, submissionId) => {
  try {
    const chatId = callbackQuery.message.chat.id;

    // Submission'ı bul ve contest ile populate et
    const submission = await Submission.findById(submissionId)
      .populate({
        path: 'contest',
        populate: {
          path: 'questions'
        }
      });

    if (!submission) {
      return bot.sendMessage(
        chatId,
        await getTranslation(user.language, 'bot.errors.general')
      );
    }

    // Cevapları Map'ten Object'e dönüştür
    const answersObj = {};
    submission.answers.forEach((value, key) => {
      answersObj[key] = value;
    });

    // Başlık mesajını hazırla
    const titleMessages = {
      tr: `📋 *${submission.contest.title} - Cevaplarınız*\n\n`,
      en: `📋 *${submission.contest.title} - Your Answers*\n\n`,
      de: `📋 *${submission.contest.title} - Ihre Antworten*\n\n`,
      ar: `📋 *${submission.contest.title} - إجاباتك*\n\n`
    };

    let message = getLocalizedMessage(user, titleMessages);

    // Doğru/yanlış emojileri
    const correctEmoji = "✅";
    const incorrectEmoji = "❌";
    const noAnswerEmoji = "⚠️";

    // Her soru için cevapları ve doğru cevapları göster
    submission.contest.questions.forEach((question, index) => {
      const questionId = question._id.toString();
      const userAnswer = answersObj[questionId];

      // Soru metni
      message += `*${index + 1}. ${question.text}*\n`;

      // Kullanıcının cevabı
      let userAnswerText = '';
      let isCorrect = false;

      if (userAnswer) {
        const selectedOption = question.options.find(opt => opt.value === userAnswer);
        userAnswerText = selectedOption ? selectedOption.text : userAnswer;
        isCorrect = question.correctAnswer === userAnswer;

        // Doğru/yanlış emoji ekle
        const emoji = isCorrect ? correctEmoji : incorrectEmoji;

        const userAnswerMessages = {
          tr: `${emoji} Cevabınız: ${userAnswerText}\n`,
          en: `${emoji} Your answer: ${userAnswerText}\n`,
          de: `${emoji} Ihre Antwort: ${userAnswerText}\n`,
          ar: `${emoji} إجابتك: ${userAnswerText}\n`
        };
        message += getLocalizedMessage(user, userAnswerMessages);
      } else {
        // Cevap verilmemiş
        const noAnswerMessages = {
          tr: `${noAnswerEmoji} Cevap verilmemiş\n`,
          en: `${noAnswerEmoji} No answer provided\n`,
          de: `${noAnswerEmoji} Keine Antwort gegeben\n`,
          ar: `${noAnswerEmoji} لم يتم تقديم إجابة\n`
        };
        message += getLocalizedMessage(user, noAnswerMessages);
      }

      // Doğru cevap (sadece tamamlanmış yarışmalar için)
      if (submission.contest.status === 'COMPLETED' && question.correctAnswer) {
        const correctOption = question.options.find(opt => opt.value === question.correctAnswer);
        const correctAnswerText = correctOption ? correctOption.text : question.correctAnswer;

        const correctAnswerMessages = {
          tr: `🔍 Doğru cevap: ${correctAnswerText}\n`,
          en: `🔍 Correct answer: ${correctAnswerText}\n`,
          de: `🔍 Richtige Antwort: ${correctAnswerText}\n`,
          ar: `🔍 الإجابة الصحيحة: ${correctAnswerText}\n`
        };
        message += getLocalizedMessage(user, correctAnswerMessages);
      }

      message += '\n';
    });

    // Toplam doğru cevap sayısı
    const summaryMessages = {
      tr: `📊 *Sonuç:* ${submission.correctAnswers} / ${submission.contest.questions.length} doğru cevap`,
      en: `📊 *Result:* ${submission.correctAnswers} / ${submission.contest.questions.length} correct answers`,
      de: `📊 *Ergebnis:* ${submission.correctAnswers} / ${submission.contest.questions.length} richtige Antworten`,
      ar: `📊 *النتيجة:* ${submission.correctAnswers} / ${submission.contest.questions.length} إجابات صحيحة`
    };
    message += getLocalizedMessage(user, summaryMessages);

    // Kazanan durumu
    if (submission.isWinner) {
      const winnerMessages = {
        tr: `\n\n🏆 *Tebrikler! Bu yarışmada kazandınız!*`,
        en: `\n\n🏆 *Congratulations! You won this contest!*`,
        de: `\n\n🏆 *Herzlichen Glückwunsch! Sie haben diesen Wettbewerb gewonnen!*`,
        ar: `\n\n🏆 *تهانينا! لقد فزت في هذه المسابقة!*`
      };
      message += getLocalizedMessage(user, winnerMessages);
    }

    // Mesajı gönder
    bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });

  } catch (error) {
    console.error('Error handling view answers:', error);
    bot.sendMessage(
      callbackQuery.message.chat.id,
      await getTranslation(user.language, 'bot.errors.general')
    );
  }
};

module.exports = {
  startBot,
  getBot,
  notifyWinners,
  sendNotificationToUser,
  sendBroadcastNotification
};
